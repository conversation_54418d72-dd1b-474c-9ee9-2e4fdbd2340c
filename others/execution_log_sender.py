import datetime
import time
import schedule
from kafka import KafkaProducer

execution_log_path = r"\\192.168.0.49\execution_test\ExecutionLog"
producer = KafkaProducer(bootstrap_servers="192.168.0.198:9092")


def follow(thefile, postion):
    while True:
        line = thefile.readline()
        if not line:
            time.sleep(1)  # Sleep briefly
            continue
        yield line


def sender():

    filename = (
        execution_log_path
        + datetime.datetime.now().date().strftime("%m-%d-%Y")
        + ".txt"
    )
    file = open(filename, mode="r")
    old_lines = file.read()

    ## Send current lines
    for line in old_lines.split("\n"):
        if line == "\n" or len(line) == 0:
            continue
        line = bytes(line, "utf-8")
        # display("Sending Old:",line)
        producer.send("logger2", line)
        producer.flush()

    ## Start sending appended lines
    for line in follow(file, 0):
        if line == "\n" or len(line) == 0:
            continue
        line = bytes(line, "utf-8")
        # display("Sending:",line)
        producer.send("logger2", line)
        producer.flush()


# schedule.every().day.at("09:21").do(sender)
# while True:
#    schedule.run_pending()
#    time.sleep(10)
sender()

# Reason for extra line
# >>> a='line1\nline2\n'
# >>> a
# 'line1\nline2\n'
# >>> a.split('\n')
# ['line1', 'line2', '']
