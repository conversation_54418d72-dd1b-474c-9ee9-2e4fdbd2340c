// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";
package ExecutionLogs;
import "google/protobuf/timestamp.proto";

enum BuySell{
  buy = 0;
  sell = 1;
}

message BaseLog {
  google.protobuf.Timestamp timestamp = 1;
  oneof log_message {
    NewOrderLog new_order_log = 2;
    SendOrderLog send_order_log = 3;
    ModificationLog modification_log = 4;
    ExecutionLog execution_log = 5;
    CancellationLog cancellation_log = 6;
    ClientOrderMsg order_confirmation = 7;
    ClientTradeMsg trade_confirmation = 8;
    ShareKhanResponse sharekhan_response = 9;
    SystemStatus system_status = 10;
  }
  int32 message_id = 11;
}

message OrderBookLog {
  int32 buy_price_1 = 1;
  int32 sell_price_1 = 2;
  int32 last_traded_price = 3;
  int64 volume_traded_today = 4;
  google.protobuf.Timestamp last_traded_time = 5;
  int64 total_buy_qty = 6;
  int64 total_sell_qty = 7;
  int32 avg_traded_price = 8;
  google.protobuf.Timestamp exch_send_time = 9;
  google.protobuf.Timestamp system_receive_time = 10;
}

message NewOrderLog {
  string instrument = 1;
  string symbol = 2;
  string expiry_date = 3;
  string option_type = 4;
  double strike = 5;
  int32 trade_qty = 6;
  BuySell buy_sell = 7;
  int64 trade_id = 8;
  double order_price = 9;
  double trigger_price = 10;
  double take_profit_price = 11;
  int32 system_id = 12;
  string expiration_time = 13;
  string strategy_name = 14;
  OrderBookLog order_book = 15;
}

message SendOrderLog {
  int32 order_id = 1;
  BuySell buy_sell = 2;
  int32 order_qty = 3;
  int64 trade_id = 4;
  string contract = 5;
  BuySell trade_buy_sell = 6;
  int32 trade_qty = 7;
  int32 order_price = 8;
  OrderBookLog order_book = 9;
}

message ModificationLog {
  int32 order_id = 1;
  BuySell buy_sell = 2;
  int32 remaining_qty = 3;
  string contract = 4;
  int32 old_order_price = 5;
  int32 modified_order_price = 6;
  OrderBookLog order_book = 7;
}

message ExecutionLog {
  int32 order_id = 1;
  BuySell buy_sell = 2;
  int32 order_qty = 3;
  int64 trade_id = 4;
  bool netoff = 5;
  string contract = 6;
  int32 trade_qty = 7;
  int32 executed_price = 8;
  OrderBookLog order_book = 9;
}

message CancellationLog {
  int32 order_id = 1;
  BuySell buy_sell = 2;
  int32 order_qty = 3;
  string contract = 4;
  int32 executed_qty = 5;
  int32 order_price = 6;
  int32 executed_price = 7;
}

message ClientOrderMsg {
  string client_order_id = 1;
  int32 order_id = 2;
  int32 order_price = 3;
  BuySell buy_sell = 4;
  int32 order_qty = 5;
  int64 trade_id = 6;
  int32 trade_qty = 7;
  int32 response_type = 8;
  string exchange_order_id = 9;
}

message ClientTradeMsg {
  string client_order_id = 1;
  int64 order_fill_number = 2;
  int32 trade_qty = 3;
  int32 trade_price = 4;
  BuySell buy_sell = 5;
  int32 response_type = 6;
  string exchange_order_id = 7;
  int32 order_qty = 8;
  int32 order_id = 9;
}

message ClientTriggerMsg {
  string client_order_id = 1;
  int64 trade_id = 2;
  int32 trade_qty = 3;
  int32 order_price = 4;
  BuySell buy_sell = 5;
  int32 response_type = 6;
  string exchange_order_id = 7;
  int32 order_qty = 8;
}

message ShareKhanResponse {
  string client_order_id = 1;
  int32 order_id = 2;
}

message SystemStatus {
  string status = 1;
}
