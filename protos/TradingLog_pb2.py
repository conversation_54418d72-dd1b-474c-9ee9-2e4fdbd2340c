# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: TradingLog.proto

import sys

_b = sys.version_info[0] < 3 and (lambda x: x) or (lambda x: x.encode("latin1"))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
    name="TradingLog.proto",
    package="ExecutionLogs",
    syntax="proto3",
    serialized_options=None,
    serialized_pb=_b(
        '\n\x10TradingLog.proto\x12\rExecutionLogs\x1a\x1fgoogle/protobuf/timestamp.proto"\xe5\x04\n\x07\x42\x61seLog\x12-\n\ttimestamp\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\rnew_order_log\x18\x02 \x01(\x0b\x32\x1a.ExecutionLogs.NewOrderLogH\x00\x12\x35\n\x0esend_order_log\x18\x03 \x01(\x0b\x32\x1b.ExecutionLogs.SendOrderLogH\x00\x12:\n\x10modification_log\x18\x04 \x01(\x0b\x32\x1e.ExecutionLogs.ModificationLogH\x00\x12\x34\n\rexecution_log\x18\x05 \x01(\x0b\x32\x1b.ExecutionLogs.ExecutionLogH\x00\x12:\n\x10\x63\x61ncellation_log\x18\x06 \x01(\x0b\x32\x1e.ExecutionLogs.CancellationLogH\x00\x12;\n\x12order_confirmation\x18\x07 \x01(\x0b\x32\x1d.ExecutionLogs.ClientOrderMsgH\x00\x12;\n\x12trade_confirmation\x18\x08 \x01(\x0b\x32\x1d.ExecutionLogs.ClientTradeMsgH\x00\x12>\n\x12sharekhan_response\x18\t \x01(\x0b\x32 .ExecutionLogs.ShareKhanResponseH\x00\x12\x34\n\rsystem_status\x18\n \x01(\x0b\x32\x1b.ExecutionLogs.SystemStatusH\x00\x12\x12\n\nmessage_id\x18\x0b \x01(\x05\x42\r\n\x0blog_message"\xdd\x02\n\x0cOrderBookLog\x12\x13\n\x0b\x62uy_price_1\x18\x01 \x01(\x05\x12\x14\n\x0csell_price_1\x18\x02 \x01(\x05\x12\x19\n\x11last_traded_price\x18\x03 \x01(\x05\x12\x1b\n\x13volume_traded_today\x18\x04 \x01(\x03\x12\x34\n\x10last_traded_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rtotal_buy_qty\x18\x06 \x01(\x03\x12\x16\n\x0etotal_sell_qty\x18\x07 \x01(\x03\x12\x18\n\x10\x61vg_traded_price\x18\x08 \x01(\x05\x12\x32\n\x0e\x65xch_send_time\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x37\n\x13system_receive_time\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp"\xf5\x02\n\x0bNewOrderLog\x12\x12\n\ninstrument\x18\x01 \x01(\t\x12\x0e\n\x06symbol\x18\x02 \x01(\t\x12\x13\n\x0b\x65xpiry_date\x18\x03 \x01(\t\x12\x13\n\x0boption_type\x18\x04 \x01(\t\x12\x0e\n\x06strike\x18\x05 \x01(\x01\x12\x11\n\ttrade_qty\x18\x06 \x01(\x05\x12(\n\x08\x62uy_sell\x18\x07 \x01(\x0e\x32\x16.ExecutionLogs.BuySell\x12\x10\n\x08trade_id\x18\x08 \x01(\x03\x12\x13\n\x0border_price\x18\t \x01(\x01\x12\x15\n\rtrigger_price\x18\n \x01(\x01\x12\x19\n\x11take_profit_price\x18\x0b \x01(\x01\x12\x11\n\tsystem_id\x18\x0c \x01(\x05\x12\x17\n\x0f\x65xpiration_time\x18\r \x01(\t\x12\x15\n\rstrategy_name\x18\x0e \x01(\t\x12/\n\norder_book\x18\x0f \x01(\x0b\x32\x1b.ExecutionLogs.OrderBookLog"\x8a\x02\n\x0cSendOrderLog\x12\x10\n\x08order_id\x18\x01 \x01(\x05\x12(\n\x08\x62uy_sell\x18\x02 \x01(\x0e\x32\x16.ExecutionLogs.BuySell\x12\x11\n\torder_qty\x18\x03 \x01(\x05\x12\x10\n\x08trade_id\x18\x04 \x01(\x03\x12\x10\n\x08\x63ontract\x18\x05 \x01(\t\x12.\n\x0etrade_buy_sell\x18\x06 \x01(\x0e\x32\x16.ExecutionLogs.BuySell\x12\x11\n\ttrade_qty\x18\x07 \x01(\x05\x12\x13\n\x0border_price\x18\x08 \x01(\x05\x12/\n\norder_book\x18\t \x01(\x0b\x32\x1b.ExecutionLogs.OrderBookLog"\xde\x01\n\x0fModificationLog\x12\x10\n\x08order_id\x18\x01 \x01(\x05\x12(\n\x08\x62uy_sell\x18\x02 \x01(\x0e\x32\x16.ExecutionLogs.BuySell\x12\x15\n\rremaining_qty\x18\x03 \x01(\x05\x12\x10\n\x08\x63ontract\x18\x04 \x01(\t\x12\x17\n\x0fold_order_price\x18\x05 \x01(\x05\x12\x1c\n\x14modified_order_price\x18\x06 \x01(\x05\x12/\n\norder_book\x18\x07 \x01(\x0b\x32\x1b.ExecutionLogs.OrderBookLog"\xed\x01\n\x0c\x45xecutionLog\x12\x10\n\x08order_id\x18\x01 \x01(\x05\x12(\n\x08\x62uy_sell\x18\x02 \x01(\x0e\x32\x16.ExecutionLogs.BuySell\x12\x11\n\torder_qty\x18\x03 \x01(\x05\x12\x10\n\x08trade_id\x18\x04 \x01(\x03\x12\x0e\n\x06netoff\x18\x05 \x01(\x08\x12\x10\n\x08\x63ontract\x18\x06 \x01(\t\x12\x11\n\ttrade_qty\x18\x07 \x01(\x05\x12\x16\n\x0e\x65xecuted_price\x18\x08 \x01(\x05\x12/\n\norder_book\x18\t \x01(\x0b\x32\x1b.ExecutionLogs.OrderBookLog"\xb5\x01\n\x0f\x43\x61ncellationLog\x12\x10\n\x08order_id\x18\x01 \x01(\x05\x12(\n\x08\x62uy_sell\x18\x02 \x01(\x0e\x32\x16.ExecutionLogs.BuySell\x12\x11\n\torder_qty\x18\x03 \x01(\x05\x12\x10\n\x08\x63ontract\x18\x04 \x01(\t\x12\x14\n\x0c\x65xecuted_qty\x18\x05 \x01(\x05\x12\x13\n\x0border_price\x18\x06 \x01(\x05\x12\x16\n\x0e\x65xecuted_price\x18\x07 \x01(\x05"\xe4\x01\n\x0e\x43lientOrderMsg\x12\x17\n\x0f\x63lient_order_id\x18\x01 \x01(\t\x12\x10\n\x08order_id\x18\x02 \x01(\x05\x12\x13\n\x0border_price\x18\x03 \x01(\x05\x12(\n\x08\x62uy_sell\x18\x04 \x01(\x0e\x32\x16.ExecutionLogs.BuySell\x12\x11\n\torder_qty\x18\x05 \x01(\x05\x12\x10\n\x08trade_id\x18\x06 \x01(\x03\x12\x11\n\ttrade_qty\x18\x07 \x01(\x05\x12\x15\n\rresponse_type\x18\x08 \x01(\x05\x12\x19\n\x11\x65xchange_order_id\x18\t \x01(\t"\xed\x01\n\x0e\x43lientTradeMsg\x12\x17\n\x0f\x63lient_order_id\x18\x01 \x01(\t\x12\x19\n\x11order_fill_number\x18\x02 \x01(\x03\x12\x11\n\ttrade_qty\x18\x03 \x01(\x05\x12\x13\n\x0btrade_price\x18\x04 \x01(\x05\x12(\n\x08\x62uy_sell\x18\x05 \x01(\x0e\x32\x16.ExecutionLogs.BuySell\x12\x15\n\rresponse_type\x18\x06 \x01(\x05\x12\x19\n\x11\x65xchange_order_id\x18\x07 \x01(\t\x12\x11\n\torder_qty\x18\x08 \x01(\x05\x12\x10\n\x08order_id\x18\t \x01(\x05"\xd4\x01\n\x10\x43lientTriggerMsg\x12\x17\n\x0f\x63lient_order_id\x18\x01 \x01(\t\x12\x10\n\x08trade_id\x18\x02 \x01(\x03\x12\x11\n\ttrade_qty\x18\x03 \x01(\x05\x12\x13\n\x0border_price\x18\x04 \x01(\x05\x12(\n\x08\x62uy_sell\x18\x05 \x01(\x0e\x32\x16.ExecutionLogs.BuySell\x12\x15\n\rresponse_type\x18\x06 \x01(\x05\x12\x19\n\x11\x65xchange_order_id\x18\x07 \x01(\t\x12\x11\n\torder_qty\x18\x08 \x01(\x05">\n\x11ShareKhanResponse\x12\x17\n\x0f\x63lient_order_id\x18\x01 \x01(\t\x12\x10\n\x08order_id\x18\x02 \x01(\x05"\x1e\n\x0cSystemStatus\x12\x0e\n\x06status\x18\x01 \x01(\t*\x1c\n\x07\x42uySell\x12\x07\n\x03\x62uy\x10\x00\x12\x08\n\x04sell\x10\x01\x62\x06proto3'
    ),
    dependencies=[
        google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,
    ],
)

_BUYSELL = _descriptor.EnumDescriptor(
    name="BuySell",
    full_name="ExecutionLogs.BuySell",
    filename=None,
    file=DESCRIPTOR,
    values=[
        _descriptor.EnumValueDescriptor(
            name="buy", index=0, number=0, serialized_options=None, type=None
        ),
        _descriptor.EnumValueDescriptor(
            name="sell", index=1, number=1, serialized_options=None, type=None
        ),
    ],
    containing_type=None,
    serialized_options=None,
    serialized_start=3112,
    serialized_end=3140,
)
_sym_db.RegisterEnumDescriptor(_BUYSELL)

BuySell = enum_type_wrapper.EnumTypeWrapper(_BUYSELL)
buy = 0
sell = 1


_BASELOG = _descriptor.Descriptor(
    name="BaseLog",
    full_name="ExecutionLogs.BaseLog",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="timestamp",
            full_name="ExecutionLogs.BaseLog.timestamp",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="new_order_log",
            full_name="ExecutionLogs.BaseLog.new_order_log",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="send_order_log",
            full_name="ExecutionLogs.BaseLog.send_order_log",
            index=2,
            number=3,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="modification_log",
            full_name="ExecutionLogs.BaseLog.modification_log",
            index=3,
            number=4,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="execution_log",
            full_name="ExecutionLogs.BaseLog.execution_log",
            index=4,
            number=5,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="cancellation_log",
            full_name="ExecutionLogs.BaseLog.cancellation_log",
            index=5,
            number=6,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_confirmation",
            full_name="ExecutionLogs.BaseLog.order_confirmation",
            index=6,
            number=7,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_confirmation",
            full_name="ExecutionLogs.BaseLog.trade_confirmation",
            index=7,
            number=8,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="sharekhan_response",
            full_name="ExecutionLogs.BaseLog.sharekhan_response",
            index=8,
            number=9,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="system_status",
            full_name="ExecutionLogs.BaseLog.system_status",
            index=9,
            number=10,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="message_id",
            full_name="ExecutionLogs.BaseLog.message_id",
            index=10,
            number=11,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[
        _descriptor.OneofDescriptor(
            name="log_message",
            full_name="ExecutionLogs.BaseLog.log_message",
            index=0,
            containing_type=None,
            fields=[],
        ),
    ],
    serialized_start=69,
    serialized_end=682,
)


_ORDERBOOKLOG = _descriptor.Descriptor(
    name="OrderBookLog",
    full_name="ExecutionLogs.OrderBookLog",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="buy_price_1",
            full_name="ExecutionLogs.OrderBookLog.buy_price_1",
            index=0,
            number=1,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="sell_price_1",
            full_name="ExecutionLogs.OrderBookLog.sell_price_1",
            index=1,
            number=2,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="last_traded_price",
            full_name="ExecutionLogs.OrderBookLog.last_traded_price",
            index=2,
            number=3,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="volume_traded_today",
            full_name="ExecutionLogs.OrderBookLog.volume_traded_today",
            index=3,
            number=4,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="last_traded_time",
            full_name="ExecutionLogs.OrderBookLog.last_traded_time",
            index=4,
            number=5,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="total_buy_qty",
            full_name="ExecutionLogs.OrderBookLog.total_buy_qty",
            index=5,
            number=6,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="total_sell_qty",
            full_name="ExecutionLogs.OrderBookLog.total_sell_qty",
            index=6,
            number=7,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="avg_traded_price",
            full_name="ExecutionLogs.OrderBookLog.avg_traded_price",
            index=7,
            number=8,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="exch_send_time",
            full_name="ExecutionLogs.OrderBookLog.exch_send_time",
            index=8,
            number=9,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="system_receive_time",
            full_name="ExecutionLogs.OrderBookLog.system_receive_time",
            index=9,
            number=10,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=685,
    serialized_end=1034,
)


_NEWORDERLOG = _descriptor.Descriptor(
    name="NewOrderLog",
    full_name="ExecutionLogs.NewOrderLog",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="instrument",
            full_name="ExecutionLogs.NewOrderLog.instrument",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="symbol",
            full_name="ExecutionLogs.NewOrderLog.symbol",
            index=1,
            number=2,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="expiry_date",
            full_name="ExecutionLogs.NewOrderLog.expiry_date",
            index=2,
            number=3,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="option_type",
            full_name="ExecutionLogs.NewOrderLog.option_type",
            index=3,
            number=4,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="strike",
            full_name="ExecutionLogs.NewOrderLog.strike",
            index=4,
            number=5,
            type=1,
            cpp_type=5,
            label=1,
            has_default_value=False,
            default_value=float(0),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_qty",
            full_name="ExecutionLogs.NewOrderLog.trade_qty",
            index=5,
            number=6,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="buy_sell",
            full_name="ExecutionLogs.NewOrderLog.buy_sell",
            index=6,
            number=7,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_id",
            full_name="ExecutionLogs.NewOrderLog.trade_id",
            index=7,
            number=8,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_price",
            full_name="ExecutionLogs.NewOrderLog.order_price",
            index=8,
            number=9,
            type=1,
            cpp_type=5,
            label=1,
            has_default_value=False,
            default_value=float(0),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trigger_price",
            full_name="ExecutionLogs.NewOrderLog.trigger_price",
            index=9,
            number=10,
            type=1,
            cpp_type=5,
            label=1,
            has_default_value=False,
            default_value=float(0),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="take_profit_price",
            full_name="ExecutionLogs.NewOrderLog.take_profit_price",
            index=10,
            number=11,
            type=1,
            cpp_type=5,
            label=1,
            has_default_value=False,
            default_value=float(0),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="system_id",
            full_name="ExecutionLogs.NewOrderLog.system_id",
            index=11,
            number=12,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="expiration_time",
            full_name="ExecutionLogs.NewOrderLog.expiration_time",
            index=12,
            number=13,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="strategy_name",
            full_name="ExecutionLogs.NewOrderLog.strategy_name",
            index=13,
            number=14,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_book",
            full_name="ExecutionLogs.NewOrderLog.order_book",
            index=14,
            number=15,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=1037,
    serialized_end=1410,
)


_SENDORDERLOG = _descriptor.Descriptor(
    name="SendOrderLog",
    full_name="ExecutionLogs.SendOrderLog",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="order_id",
            full_name="ExecutionLogs.SendOrderLog.order_id",
            index=0,
            number=1,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="buy_sell",
            full_name="ExecutionLogs.SendOrderLog.buy_sell",
            index=1,
            number=2,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_qty",
            full_name="ExecutionLogs.SendOrderLog.order_qty",
            index=2,
            number=3,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_id",
            full_name="ExecutionLogs.SendOrderLog.trade_id",
            index=3,
            number=4,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="contract",
            full_name="ExecutionLogs.SendOrderLog.contract",
            index=4,
            number=5,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_buy_sell",
            full_name="ExecutionLogs.SendOrderLog.trade_buy_sell",
            index=5,
            number=6,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_qty",
            full_name="ExecutionLogs.SendOrderLog.trade_qty",
            index=6,
            number=7,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_price",
            full_name="ExecutionLogs.SendOrderLog.order_price",
            index=7,
            number=8,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_book",
            full_name="ExecutionLogs.SendOrderLog.order_book",
            index=8,
            number=9,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=1413,
    serialized_end=1679,
)


_MODIFICATIONLOG = _descriptor.Descriptor(
    name="ModificationLog",
    full_name="ExecutionLogs.ModificationLog",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="order_id",
            full_name="ExecutionLogs.ModificationLog.order_id",
            index=0,
            number=1,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="buy_sell",
            full_name="ExecutionLogs.ModificationLog.buy_sell",
            index=1,
            number=2,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="remaining_qty",
            full_name="ExecutionLogs.ModificationLog.remaining_qty",
            index=2,
            number=3,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="contract",
            full_name="ExecutionLogs.ModificationLog.contract",
            index=3,
            number=4,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="old_order_price",
            full_name="ExecutionLogs.ModificationLog.old_order_price",
            index=4,
            number=5,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="modified_order_price",
            full_name="ExecutionLogs.ModificationLog.modified_order_price",
            index=5,
            number=6,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_book",
            full_name="ExecutionLogs.ModificationLog.order_book",
            index=6,
            number=7,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=1682,
    serialized_end=1904,
)


_EXECUTIONLOG = _descriptor.Descriptor(
    name="ExecutionLog",
    full_name="ExecutionLogs.ExecutionLog",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="order_id",
            full_name="ExecutionLogs.ExecutionLog.order_id",
            index=0,
            number=1,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="buy_sell",
            full_name="ExecutionLogs.ExecutionLog.buy_sell",
            index=1,
            number=2,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_qty",
            full_name="ExecutionLogs.ExecutionLog.order_qty",
            index=2,
            number=3,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_id",
            full_name="ExecutionLogs.ExecutionLog.trade_id",
            index=3,
            number=4,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="netoff",
            full_name="ExecutionLogs.ExecutionLog.netoff",
            index=4,
            number=5,
            type=8,
            cpp_type=7,
            label=1,
            has_default_value=False,
            default_value=False,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="contract",
            full_name="ExecutionLogs.ExecutionLog.contract",
            index=5,
            number=6,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_qty",
            full_name="ExecutionLogs.ExecutionLog.trade_qty",
            index=6,
            number=7,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="executed_price",
            full_name="ExecutionLogs.ExecutionLog.executed_price",
            index=7,
            number=8,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_book",
            full_name="ExecutionLogs.ExecutionLog.order_book",
            index=8,
            number=9,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=1907,
    serialized_end=2144,
)


_CANCELLATIONLOG = _descriptor.Descriptor(
    name="CancellationLog",
    full_name="ExecutionLogs.CancellationLog",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="order_id",
            full_name="ExecutionLogs.CancellationLog.order_id",
            index=0,
            number=1,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="buy_sell",
            full_name="ExecutionLogs.CancellationLog.buy_sell",
            index=1,
            number=2,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_qty",
            full_name="ExecutionLogs.CancellationLog.order_qty",
            index=2,
            number=3,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="contract",
            full_name="ExecutionLogs.CancellationLog.contract",
            index=3,
            number=4,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="executed_qty",
            full_name="ExecutionLogs.CancellationLog.executed_qty",
            index=4,
            number=5,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_price",
            full_name="ExecutionLogs.CancellationLog.order_price",
            index=5,
            number=6,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="executed_price",
            full_name="ExecutionLogs.CancellationLog.executed_price",
            index=6,
            number=7,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=2147,
    serialized_end=2328,
)


_CLIENTORDERMSG = _descriptor.Descriptor(
    name="ClientOrderMsg",
    full_name="ExecutionLogs.ClientOrderMsg",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="client_order_id",
            full_name="ExecutionLogs.ClientOrderMsg.client_order_id",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_id",
            full_name="ExecutionLogs.ClientOrderMsg.order_id",
            index=1,
            number=2,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_price",
            full_name="ExecutionLogs.ClientOrderMsg.order_price",
            index=2,
            number=3,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="buy_sell",
            full_name="ExecutionLogs.ClientOrderMsg.buy_sell",
            index=3,
            number=4,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_qty",
            full_name="ExecutionLogs.ClientOrderMsg.order_qty",
            index=4,
            number=5,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_id",
            full_name="ExecutionLogs.ClientOrderMsg.trade_id",
            index=5,
            number=6,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_qty",
            full_name="ExecutionLogs.ClientOrderMsg.trade_qty",
            index=6,
            number=7,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="response_type",
            full_name="ExecutionLogs.ClientOrderMsg.response_type",
            index=7,
            number=8,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="exchange_order_id",
            full_name="ExecutionLogs.ClientOrderMsg.exchange_order_id",
            index=8,
            number=9,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=2331,
    serialized_end=2559,
)


_CLIENTTRADEMSG = _descriptor.Descriptor(
    name="ClientTradeMsg",
    full_name="ExecutionLogs.ClientTradeMsg",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="client_order_id",
            full_name="ExecutionLogs.ClientTradeMsg.client_order_id",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_fill_number",
            full_name="ExecutionLogs.ClientTradeMsg.order_fill_number",
            index=1,
            number=2,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_qty",
            full_name="ExecutionLogs.ClientTradeMsg.trade_qty",
            index=2,
            number=3,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_price",
            full_name="ExecutionLogs.ClientTradeMsg.trade_price",
            index=3,
            number=4,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="buy_sell",
            full_name="ExecutionLogs.ClientTradeMsg.buy_sell",
            index=4,
            number=5,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="response_type",
            full_name="ExecutionLogs.ClientTradeMsg.response_type",
            index=5,
            number=6,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="exchange_order_id",
            full_name="ExecutionLogs.ClientTradeMsg.exchange_order_id",
            index=6,
            number=7,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_qty",
            full_name="ExecutionLogs.ClientTradeMsg.order_qty",
            index=7,
            number=8,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_id",
            full_name="ExecutionLogs.ClientTradeMsg.order_id",
            index=8,
            number=9,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=2562,
    serialized_end=2799,
)


_CLIENTTRIGGERMSG = _descriptor.Descriptor(
    name="ClientTriggerMsg",
    full_name="ExecutionLogs.ClientTriggerMsg",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="client_order_id",
            full_name="ExecutionLogs.ClientTriggerMsg.client_order_id",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_id",
            full_name="ExecutionLogs.ClientTriggerMsg.trade_id",
            index=1,
            number=2,
            type=3,
            cpp_type=2,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="trade_qty",
            full_name="ExecutionLogs.ClientTriggerMsg.trade_qty",
            index=2,
            number=3,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_price",
            full_name="ExecutionLogs.ClientTriggerMsg.order_price",
            index=3,
            number=4,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="buy_sell",
            full_name="ExecutionLogs.ClientTriggerMsg.buy_sell",
            index=4,
            number=5,
            type=14,
            cpp_type=8,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="response_type",
            full_name="ExecutionLogs.ClientTriggerMsg.response_type",
            index=5,
            number=6,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="exchange_order_id",
            full_name="ExecutionLogs.ClientTriggerMsg.exchange_order_id",
            index=6,
            number=7,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_qty",
            full_name="ExecutionLogs.ClientTriggerMsg.order_qty",
            index=7,
            number=8,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=2802,
    serialized_end=3014,
)


_SHAREKHANRESPONSE = _descriptor.Descriptor(
    name="ShareKhanResponse",
    full_name="ExecutionLogs.ShareKhanResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="client_order_id",
            full_name="ExecutionLogs.ShareKhanResponse.client_order_id",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="order_id",
            full_name="ExecutionLogs.ShareKhanResponse.order_id",
            index=1,
            number=2,
            type=5,
            cpp_type=1,
            label=1,
            has_default_value=False,
            default_value=0,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=3016,
    serialized_end=3078,
)


_SYSTEMSTATUS = _descriptor.Descriptor(
    name="SystemStatus",
    full_name="ExecutionLogs.SystemStatus",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="status",
            full_name="ExecutionLogs.SystemStatus.status",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=3080,
    serialized_end=3110,
)

_BASELOG.fields_by_name[
    "timestamp"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BASELOG.fields_by_name["new_order_log"].message_type = _NEWORDERLOG
_BASELOG.fields_by_name["send_order_log"].message_type = _SENDORDERLOG
_BASELOG.fields_by_name["modification_log"].message_type = _MODIFICATIONLOG
_BASELOG.fields_by_name["execution_log"].message_type = _EXECUTIONLOG
_BASELOG.fields_by_name["cancellation_log"].message_type = _CANCELLATIONLOG
_BASELOG.fields_by_name["order_confirmation"].message_type = _CLIENTORDERMSG
_BASELOG.fields_by_name["trade_confirmation"].message_type = _CLIENTTRADEMSG
_BASELOG.fields_by_name["sharekhan_response"].message_type = _SHAREKHANRESPONSE
_BASELOG.fields_by_name["system_status"].message_type = _SYSTEMSTATUS
_BASELOG.oneofs_by_name["log_message"].fields.append(
    _BASELOG.fields_by_name["new_order_log"]
)
_BASELOG.fields_by_name["new_order_log"].containing_oneof = _BASELOG.oneofs_by_name[
    "log_message"
]
_BASELOG.oneofs_by_name["log_message"].fields.append(
    _BASELOG.fields_by_name["send_order_log"]
)
_BASELOG.fields_by_name["send_order_log"].containing_oneof = _BASELOG.oneofs_by_name[
    "log_message"
]
_BASELOG.oneofs_by_name["log_message"].fields.append(
    _BASELOG.fields_by_name["modification_log"]
)
_BASELOG.fields_by_name["modification_log"].containing_oneof = _BASELOG.oneofs_by_name[
    "log_message"
]
_BASELOG.oneofs_by_name["log_message"].fields.append(
    _BASELOG.fields_by_name["execution_log"]
)
_BASELOG.fields_by_name["execution_log"].containing_oneof = _BASELOG.oneofs_by_name[
    "log_message"
]
_BASELOG.oneofs_by_name["log_message"].fields.append(
    _BASELOG.fields_by_name["cancellation_log"]
)
_BASELOG.fields_by_name["cancellation_log"].containing_oneof = _BASELOG.oneofs_by_name[
    "log_message"
]
_BASELOG.oneofs_by_name["log_message"].fields.append(
    _BASELOG.fields_by_name["order_confirmation"]
)
_BASELOG.fields_by_name[
    "order_confirmation"
].containing_oneof = _BASELOG.oneofs_by_name["log_message"]
_BASELOG.oneofs_by_name["log_message"].fields.append(
    _BASELOG.fields_by_name["trade_confirmation"]
)
_BASELOG.fields_by_name[
    "trade_confirmation"
].containing_oneof = _BASELOG.oneofs_by_name["log_message"]
_BASELOG.oneofs_by_name["log_message"].fields.append(
    _BASELOG.fields_by_name["sharekhan_response"]
)
_BASELOG.fields_by_name[
    "sharekhan_response"
].containing_oneof = _BASELOG.oneofs_by_name["log_message"]
_BASELOG.oneofs_by_name["log_message"].fields.append(
    _BASELOG.fields_by_name["system_status"]
)
_BASELOG.fields_by_name["system_status"].containing_oneof = _BASELOG.oneofs_by_name[
    "log_message"
]
_ORDERBOOKLOG.fields_by_name[
    "last_traded_time"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ORDERBOOKLOG.fields_by_name[
    "exch_send_time"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ORDERBOOKLOG.fields_by_name[
    "system_receive_time"
].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_NEWORDERLOG.fields_by_name["buy_sell"].enum_type = _BUYSELL
_NEWORDERLOG.fields_by_name["order_book"].message_type = _ORDERBOOKLOG
_SENDORDERLOG.fields_by_name["buy_sell"].enum_type = _BUYSELL
_SENDORDERLOG.fields_by_name["trade_buy_sell"].enum_type = _BUYSELL
_SENDORDERLOG.fields_by_name["order_book"].message_type = _ORDERBOOKLOG
_MODIFICATIONLOG.fields_by_name["buy_sell"].enum_type = _BUYSELL
_MODIFICATIONLOG.fields_by_name["order_book"].message_type = _ORDERBOOKLOG
_EXECUTIONLOG.fields_by_name["buy_sell"].enum_type = _BUYSELL
_EXECUTIONLOG.fields_by_name["order_book"].message_type = _ORDERBOOKLOG
_CANCELLATIONLOG.fields_by_name["buy_sell"].enum_type = _BUYSELL
_CLIENTORDERMSG.fields_by_name["buy_sell"].enum_type = _BUYSELL
_CLIENTTRADEMSG.fields_by_name["buy_sell"].enum_type = _BUYSELL
_CLIENTTRIGGERMSG.fields_by_name["buy_sell"].enum_type = _BUYSELL
DESCRIPTOR.message_types_by_name["BaseLog"] = _BASELOG
DESCRIPTOR.message_types_by_name["OrderBookLog"] = _ORDERBOOKLOG
DESCRIPTOR.message_types_by_name["NewOrderLog"] = _NEWORDERLOG
DESCRIPTOR.message_types_by_name["SendOrderLog"] = _SENDORDERLOG
DESCRIPTOR.message_types_by_name["ModificationLog"] = _MODIFICATIONLOG
DESCRIPTOR.message_types_by_name["ExecutionLog"] = _EXECUTIONLOG
DESCRIPTOR.message_types_by_name["CancellationLog"] = _CANCELLATIONLOG
DESCRIPTOR.message_types_by_name["ClientOrderMsg"] = _CLIENTORDERMSG
DESCRIPTOR.message_types_by_name["ClientTradeMsg"] = _CLIENTTRADEMSG
DESCRIPTOR.message_types_by_name["ClientTriggerMsg"] = _CLIENTTRIGGERMSG
DESCRIPTOR.message_types_by_name["ShareKhanResponse"] = _SHAREKHANRESPONSE
DESCRIPTOR.message_types_by_name["SystemStatus"] = _SYSTEMSTATUS
DESCRIPTOR.enum_types_by_name["BuySell"] = _BUYSELL
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

BaseLog = _reflection.GeneratedProtocolMessageType(
    "BaseLog",
    (_message.Message,),
    dict(
        DESCRIPTOR=_BASELOG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.BaseLog)
    ),
)
_sym_db.RegisterMessage(BaseLog)

OrderBookLog = _reflection.GeneratedProtocolMessageType(
    "OrderBookLog",
    (_message.Message,),
    dict(
        DESCRIPTOR=_ORDERBOOKLOG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.OrderBookLog)
    ),
)
_sym_db.RegisterMessage(OrderBookLog)

NewOrderLog = _reflection.GeneratedProtocolMessageType(
    "NewOrderLog",
    (_message.Message,),
    dict(
        DESCRIPTOR=_NEWORDERLOG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.NewOrderLog)
    ),
)
_sym_db.RegisterMessage(NewOrderLog)

SendOrderLog = _reflection.GeneratedProtocolMessageType(
    "SendOrderLog",
    (_message.Message,),
    dict(
        DESCRIPTOR=_SENDORDERLOG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.SendOrderLog)
    ),
)
_sym_db.RegisterMessage(SendOrderLog)

ModificationLog = _reflection.GeneratedProtocolMessageType(
    "ModificationLog",
    (_message.Message,),
    dict(
        DESCRIPTOR=_MODIFICATIONLOG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.ModificationLog)
    ),
)
_sym_db.RegisterMessage(ModificationLog)

ExecutionLog = _reflection.GeneratedProtocolMessageType(
    "ExecutionLog",
    (_message.Message,),
    dict(
        DESCRIPTOR=_EXECUTIONLOG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.ExecutionLog)
    ),
)
_sym_db.RegisterMessage(ExecutionLog)

CancellationLog = _reflection.GeneratedProtocolMessageType(
    "CancellationLog",
    (_message.Message,),
    dict(
        DESCRIPTOR=_CANCELLATIONLOG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.CancellationLog)
    ),
)
_sym_db.RegisterMessage(CancellationLog)

ClientOrderMsg = _reflection.GeneratedProtocolMessageType(
    "ClientOrderMsg",
    (_message.Message,),
    dict(
        DESCRIPTOR=_CLIENTORDERMSG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.ClientOrderMsg)
    ),
)
_sym_db.RegisterMessage(ClientOrderMsg)

ClientTradeMsg = _reflection.GeneratedProtocolMessageType(
    "ClientTradeMsg",
    (_message.Message,),
    dict(
        DESCRIPTOR=_CLIENTTRADEMSG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.ClientTradeMsg)
    ),
)
_sym_db.RegisterMessage(ClientTradeMsg)

ClientTriggerMsg = _reflection.GeneratedProtocolMessageType(
    "ClientTriggerMsg",
    (_message.Message,),
    dict(
        DESCRIPTOR=_CLIENTTRIGGERMSG,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.ClientTriggerMsg)
    ),
)
_sym_db.RegisterMessage(ClientTriggerMsg)

ShareKhanResponse = _reflection.GeneratedProtocolMessageType(
    "ShareKhanResponse",
    (_message.Message,),
    dict(
        DESCRIPTOR=_SHAREKHANRESPONSE,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.ShareKhanResponse)
    ),
)
_sym_db.RegisterMessage(ShareKhanResponse)

SystemStatus = _reflection.GeneratedProtocolMessageType(
    "SystemStatus",
    (_message.Message,),
    dict(
        DESCRIPTOR=_SYSTEMSTATUS,
        __module__="TradingLog_pb2",
        # @@protoc_insertion_point(class_scope:ExecutionLogs.SystemStatus)
    ),
)
_sym_db.RegisterMessage(SystemStatus)


# @@protoc_insertion_point(module_scope)
