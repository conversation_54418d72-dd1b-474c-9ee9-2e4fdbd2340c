import pandas as pd
from logit.main import Logit
from typing import Tuple
import ipywidgets as widgets
from IPython.display import display
import qgrid
import datetime as dt
import gzip
from io import BytesIO
import numpy as np
from logit.utility import (
    compare_dataframe,
    create_matlab_tally_pending_df,
    compare_df_with_quantities,
    create_pos_tally_error_df,
    get_running_mtm_cur,
    get_today_pnl,
    get_running_mtm,
    minio_file_downloader,
    get_contract_name,
    get_engine,
    get_running_mtm_comm,
    get_today_pnl_comm,
    get_today_pnl_krx,
    get_today_pnl_us,
    get_running_mtm_krx,
    get_running_mtm_us,
    upload_df_to_minio,
    fetch_df_from_minio,
    previous_date,
    dead_sheet,
    live_sheet,
    apply_corporate_actions,
    get_running_mtm_gift,
    get_today_pnl_gift,
    download_file,
)
from dateutil import parser
from logit.data_utility import get_ltp
import logit.config as config
import copy
import os
from sqlalchemy import text


def str2num(val: str) -> float:
    """Function to convert string to number.

    Args:
        val (str): given string

    Returns:
        val (float): converted value
    """
    if val == "":
        return 0
    else:
        return float(val)


def update_df(obj: Logit):
    """Function to update object df (df_live, df_dead & df_pending).

    Args:
        obj (Logit): Logit object
    """
    obj.df_live = pd.DataFrame.from_dict(obj.live, orient="index")
    obj.df_dead = pd.DataFrame.from_dict(obj.dead, orient="index")
    obj.df_pending = pd.DataFrame.from_dict(obj.pending, orient="index")


def update_counts(obj: Logit):
    """Function to update count of live, pending and dead orders.

    Args:
        obj (Logit): logit object
    """
    obj.live_count.value = str(len(obj.live))
    obj.dead_count.value = str(len(obj.dead))
    obj.pending_count.value = str(len(obj.pending))


def update_df_view(_event: widgets, df: pd.DataFrame, obj: Logit, out: widgets):
    """Function to update df view visible to the user.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            obj.qgrid_widget = qgrid.show_grid(df, show_toolbar=True)
            display(obj.qgrid_widget)
        except Exception as e:
            display(e)


def save_df_changes(
    _event: widgets, obj: Logit, view: str, tmp_df: pd.DataFrame, out: widgets
):
    """Function to save the changed made by user in the view df to actual df.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        view (str): view selected by the user
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            if view == "PENDING":
                obj.pending = tmp_df.to_dict("index")
                obj.df_pending = tmp_df
            elif view == "LIVE":
                obj.live = tmp_df.to_dict("index")
                obj.df_live = tmp_df
            else:
                obj.dead = tmp_df.to_dict("index")
                obj.df_dead = tmp_df
            display("Your changes have been applied")
            obj.scan_pending_again()
            update_df(obj=obj)
            update_counts(obj=obj)
        except Exception as e:
            display(e)


def modify_ltp(df: pd.DataFrame, symbols: list, prices: list) -> pd.DataFrame:
    """Function to modify last traded price of given symbols and prices.

    Args:
        df (pd.DataFrame): LTP df
        symbols (list): list of symols to change
        prices (list): list of prices to change

    Raises:
        ValueError: if the length of symbol list does not match length of price list

    Returns:
        pd.DataFrame: LTP df after updating
    """
    if len(symbols) != len(prices):
        raise ValueError("Length of symbols and prices list should match!")
    for symbol, price in zip(symbols, prices):
        df.loc[symbol, "ltp"] = price
    return df


def get_ltp_notebook(
    _event: widgets,
    obj: Logit,
    hour1: str,
    minute1: str,
    symbols: list,
    prices: list,
    add_error_trade: str,
    out: widgets,
):
    """Function to get last traded price at spcified time.
        List of symbols and prices can also be passed to overwrite LTP.
        It also has an option to add as error trade.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        hour1 (str): specified hour
        minute1 (str): specified minutes
        symbols (list): list of symbols
        prices (list): list of prices
        add_error_trade (str): specifies if trades are to be added as error trades
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            df_close = obj.ltp.set_index("contract")
            df_close = modify_ltp(df=df_close, symbols=symbols, prices=prices)
            start_time = dt.datetime(
                dt.datetime.now().year,
                dt.datetime.now().month,
                dt.datetime.now().day,
                hour1,
                minute1,
            )
            error_id = 610001
            df_pending_error = obj.df_pending[obj.df_pending.strategy == "notastrategy"]
            tradeids = list(obj.df_pending.index)
            for i in tradeids:
                contract_name = get_contract_name(
                    obj.df_pending.loc[i].segment,
                    obj.df_pending.loc[i].symbol,
                    obj.df_pending.loc[i].expiry,
                    obj.df_pending.loc[i].type,
                    obj.df_pending.loc[i].strike,
                )
                if "." in contract_name:
                    contract_name = contract_name.rstrip("0").rstrip(".")
                if contract_name in df_close.index:
                    if (
                        obj.df_pending.loc[i].total_quantity
                        != obj.df_pending.loc[i].executed_quantity
                    ):
                        while (error_id in obj.live) | (error_id in obj.dead):
                            error_id += 1
                        temp_qty = int(
                            obj.df_pending.loc[i, "executed_quantity"]
                            - obj.df_pending.loc[i, "total_quantity"]
                        )
                        temp_price = df_close.loc[contract_name, "ltp"]
                        df_pending_error.loc[int("10011" + str(error_id))] = [
                            start_time,
                            obj.df_pending.loc[i, "segment"],
                            obj.df_pending.loc[i, "symbol"],
                            obj.df_pending.loc[i, "expiry"],
                            temp_qty,
                            "error",
                            temp_price,
                            obj.df_pending.loc[i, "type"],
                            obj.df_pending.loc[i, "strike"],
                            temp_qty,
                            df_close.loc[contract_name, "ltp"],
                        ]
                        obj.df_pending.loc[i, "weighted_price"] = (
                            abs(obj.df_pending.loc[i, "executed_quantity"])
                            * obj.df_pending.loc[i, "weighted_price"]
                            + abs(temp_qty) * temp_price
                        ) / abs(obj.df_pending.loc[i, "total_quantity"])
                        obj.df_pending.loc[i, "executed_quantity"] = obj.df_pending.loc[
                            i, "total_quantity"
                        ]
                        error_id += 1

            if add_error_trade == "Yes":
                obj.df_pending = obj.df_pending.append(df_pending_error)
            obj.pending = obj.df_pending.to_dict("index")
            update_df(obj=obj)
            update_counts(obj=obj)
        except Exception as e:
            display(e)


def error_order(_event: widgets, obj: Logit, out: widgets, flag_mcx: bool = False):
    """Function to create error trade.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
        flag_mcx (bool, optional): flag  specifying if we are trading in commodities
    """
    with out:
        out.clear_output()
        try:
            selected_rows = obj.qgrid_widget.get_selected_rows()
            trade_ids = obj.df_pending.index[selected_rows]
            obj.create_error_trade(list(trade_ids))
            if flag_mcx is True:
                for ids in obj.live:
                    obj.live[ids]["multiplier"] = obj.store_lot_size[
                        obj.live[ids]["symbol"]
                    ]
            update_counts(obj=obj)
            update_df(obj=obj)
        except Exception as e:
            display(e)


def generate_error_string_df(_event: widgets, obj: Logit, out: widgets):
    """Function to generate error trades using df view.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            selected_rows = obj.qgrid_widget.get_selected_rows()
            trade_ids = obj.df_live.index[selected_rows]
            obj.get_order_string_for_error(list(trade_ids))
        except Exception as e:
            display(e)


def generate_error_string_seg_strat(
    _event: widgets, obj: Logit, strategy: str, segment: str, out: widgets
):
    """Function to generate error string using strategy and segment name.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        strategy (str): specified strategy
        segment (str): specified segment
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            trade_ids = obj.df_live.index[
                (obj.df_live.strategy == strategy) & (obj.df_live.segment == segment)
            ]
            obj.get_order_string_for_error(list(trade_ids))
        except Exception as e:
            display(e)


def exit_orders_manually(
    _event: widgets,
    obj: Logit,
    indexids: list,
    indexprice: list,
    sharekhan: bool,
    out: widgets,
):
    """Function to exit orders manually.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        indexids (list): list of id(s) to exit
        indexprice (list): list of price(s) to exit
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            for tradeID, exit_price in zip(indexids, indexprice):
                obj.store_sharekhan[int(tradeID)] = 0 if sharekhan else 1
                obj.exit_error_manually(tradeID, exit_price)  # TradeID , Exit Price
            update_counts(obj=obj)
            update_df(obj=obj)
        except Exception as e:
            display(e)


def enter_orders_manually(
    _event: widgets,
    obj: Logit,
    id: int,
    segment: str,
    symbol: str,
    expiry: str,
    quantity: int,
    strategy: str,
    wgt_price: float,
    type: str,
    strike: float,
    out: widgets,
):
    """Function to enter orders manually.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        id (int): logit id
        segment (str): segment
        symbol (str): symbol
        expiry (str): expiry date
        quantity (int): quantity
        strategy (str): strategy name
        wgt_price (int): weighted price
        type (str): CE, PE or XX
        strike (float): strike price
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            if obj.mode != "KRX":
                obj.live[id] = {
                    "timestamp": pd.Timestamp.now(),
                    "segment": segment,
                    "symbol": symbol,
                    "expiry": expiry,
                    "total_quantity": quantity,
                    "strategy": strategy,
                    "weighted_price": wgt_price,
                    "type": type,
                    "strike": strike,
                }
            else:
                obj.live[id] = {
                    "timestamp": pd.Timestamp.now(tz="Asia/Seoul").replace(tzinfo=None),
                    "segment": segment,
                    "symbol": symbol,
                    "expiry": expiry,
                    "total_quantity": quantity,
                    "strategy": strategy,
                    "weighted_price": wgt_price,
                    "type": type,
                    "strike": strike,
                }
            update_counts(obj=obj)
            update_df(obj=obj)
            obj.live = obj.df_live.to_dict(orient="index")
            assert id in obj.live
            update_df(obj=obj)
        except Exception as e:
            display(e)


def fetch_positions_from_db(obj: Logit):
    """Function to fetch balte positions from database

    Args:
        obj (Logit): given logit object.
    """
    table_name = eval(f"config.BALTE_LIVE_TABLE_{obj.mode}")
    engine = obj.centralised_oms_engine
    if obj.mode in ["NSE", "COMM"]:
        engine = obj.nse_oms_engine
    balte_pos = pd.read_sql(f"SELECT * FROM {table_name}", con=engine)
    balte_pos = balte_pos[~balte_pos.STRATEGY.str.contains("BaLTE_")]
    balte_pos = balte_pos.sort_values("STRATEGY")
    balte = pd.DataFrame(
        {
            0: balte_pos["ENTRY_TIMESTAMP"],
            1: balte_pos["STRATEGY"],
            2: balte_pos["SEGMENT"],
            3: balte_pos["SYMBOL"],
            4: balte_pos["EXPIRY"],
            5: balte_pos["TYPE"],
            6: balte_pos["STRIKE"],
            7: balte_pos["TRADEID"],
            8: balte_pos["QUANTITY"],
            9: balte_pos["ENTRY_PRICE"],
        }
    )
    balte.set_index(7, inplace=True)
    balte[0] = pd.to_datetime(balte[0])
    balte[4] = pd.to_datetime(balte[4])
    return balte


def form_position_data(obj: Logit) -> Tuple[pd.DataFrame, list]:
    """Function to form position tally data.

    Args:
        obj (Logit): logit object

    Returns:
        Tuple[pd.DataFrame, list]: matlab df and pending trade ids
    """
    try:
        balte = fetch_positions_from_db(obj=obj)
    except Exception:
        balte = pd.DataFrame()
    matlab = balte
    if len(obj.df_pending) != 0:
        trade_ids_in_pending = obj.df_pending.index.astype("str").str[5:].values
    else:
        trade_ids_in_pending = []
    return matlab, trade_ids_in_pending


def form_position_data_mcx(obj: Logit) -> Tuple[pd.DataFrame, list]:
    """Function to form position tally data for mcx.

    Args:
        obj (Logit): logit object

    Returns:
        Tuple[pd.DataFrame, list]: matlab df and pending trade ids
    """
    try:
        matlab = fetch_positions_from_db(obj=obj)
    except Exception:
        matlab = pd.DataFrame()
    try:
        matlab_ncdex = fetch_positions_from_db(obj=obj)
    except Exception:
        matlab_ncdex = pd.DataFrame()
    matlab = pd.concat([matlab, matlab_ncdex])
    for i in list(matlab.index):
        matlab.at[i, 8] = round(matlab.at[i, 8] / obj.store_lot_size[matlab.at[i, 3]])
    if len(obj.df_pending) != 0:
        trade_ids_in_pending = obj.df_pending.index.astype("str").str[-6:].values
    else:
        trade_ids_in_pending = []
    return matlab, trade_ids_in_pending


def form_position_data_krx(obj: Logit) -> Tuple[pd.DataFrame, list]:
    """Function to form position tally data.

    Args:
        obj (Logit): logit object

    Returns:
        Tuple[pd.DataFrame, list]: matlab df and pending trade ids
    """
    try:
        balte = pd.read_sql("SELECT * FROM krx_live", con=obj.centralised_oms_engine)
    except pd.errors.EmptyDataError:
        pass
    matlab = balte
    if len(obj.df_pending) != 0:
        trade_ids_in_pending = obj.df_pending.index.astype("str").str[5:].values
    else:
        trade_ids_in_pending = []
    return matlab, trade_ids_in_pending


def form_position_data_us(obj: Logit) -> Tuple[pd.DataFrame, list]:
    """Function to form position tally data.

    Args:
        obj (Logit): logit object

    Returns:
        Tuple[pd.DataFrame, list]: matlab df and pending trade ids
    """
    try:
        balte = pd.read_sql(
            "SELECT * FROM us_production", con=obj.centralised_oms_engine
        )
    except pd.errors.EmptyDataError:
        pass
    matlab = balte
    if len(obj.df_pending) != 0:
        trade_ids_in_pending = obj.df_pending.index.astype("str").str[5:].values
    else:
        trade_ids_in_pending = []
    return matlab, trade_ids_in_pending


def tally_position_with_matlab(_event: widgets, obj: Logit, out: widgets):
    """Function to tally position with matlab.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            if obj.mode == "NSE":
                matlab, trade_ids_in_pending = form_position_data(obj=obj)
            elif obj.mode == "COMM":
                matlab, trade_ids_in_pending = form_position_data_mcx(obj=obj)
            elif obj.mode == "KRX":
                matlab, trade_ids_in_pending = form_position_data_krx(obj=obj)
            elif obj.mode == "US":
                matlab, trade_ids_in_pending = form_position_data_us(obj=obj)
            compare_dataframe(
                obj.df_live,
                matlab,
                ["total_quantity"],
                [8],
                return_full_df=False,
                ignore=pd.Series(trade_ids_in_pending),
            )
        except Exception as e:
            display(e)


def add_matlab_trade_to_pending_orders(_event: widgets, obj: Logit, out: widgets):
    """Function to add matlab mismatch trades to pending orders.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            matlab, trade_ids_in_pending = form_position_data(obj=obj)
            [df_logger, df_matlab] = compare_dataframe(
                obj.df_live,
                matlab,
                ["total_quantity"],
                [8],
                return_full_df=False,
                ignore=pd.Series(trade_ids_in_pending),
                return_value=True,
            )
            df_logger = df_logger[
                (df_logger["strategy"].isin(["error", "manual_order"]))
            ]
            df_logger = df_logger[
                (
                    df_logger["segment"].isin(
                        [
                            "OPTIDX",
                            "OPTSTK",
                            "FUTSTK",
                            "OPTIDX_BSE",
                            "OPTSTK_BSE",
                            "FUTSTK_BSE",
                        ]
                    )
                )
            ]
            df_pending_temp = create_matlab_tally_pending_df(df_logger, df_matlab)
            obj.df_pending = obj.df_pending.append(df_pending_temp)
            obj.pending = obj.df_pending.to_dict("index")
            update_counts(obj=obj)
            update_df(obj=obj)
            print("Mismatch added to pending orders")
        except Exception as e:
            display(e)


def form_tally_data(obj: Logit) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Function to form turnover tally data.

    Args:
        obj (Logit): logit object

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: logit position and sharekhan data
    """
    minio_file_downloader("execsystem", "turnover_sk.txt", "temp.csv")
    sharekhan = pd.read_csv(
        "temp.csv", header=None, usecols=[0, 9], names=["symbol", "total_quantity"]
    )
    minio_file_downloader("execsystem", "turnover_greek.txt", "temp.csv")
    greek_turnover = pd.read_csv(
        "temp.csv", header=None, usecols=[0, 2], names=["symbol", "total_quantity"]
    )
    minio_file_downloader("execsystem", "turnover_bse.txt", "temp.csv")
    bse_turnover = pd.read_csv(
        "temp.csv", header=None, usecols=[0, 2], names=["symbol", "total_quantity"]
    )
    sharekhan = pd.concat([sharekhan, greek_turnover, bse_turnover], ignore_index=True)
    sharekhan = sharekhan.set_index(["symbol"]).groupby(level=0).sum()
    logit_pos = obj.df_live[
        (obj.df_live["segment"] == "FUTSTK")
        | (obj.df_live["segment"] == "FUTIDX")
        | (obj.df_live["segment"] == "FUTCUR")
    ].copy()
    logit_pos["symbol"] = logit_pos["symbol"].map(str) + logit_pos["expiry"]

    only_options = obj.df_live[
        obj.df_live["segment"].isin(config.OPTIONS_UNIVERSE_MAPPING["NSE"])
    ].copy()
    only_options["symbol"] = (
        only_options["symbol"].map(str)
        + only_options["expiry"]
        + only_options["type"]
        + only_options["strike"].map(str)
    )
    only_options["symbol"] = only_options["symbol"].apply(
        lambda x: x.rstrip("0").rstrip(".") if "." in x else x
    )
    logit_pos = pd.concat([logit_pos, only_options])
    logit_pos = pd.DataFrame(logit_pos.groupby("symbol")["total_quantity"].sum())
    return logit_pos, sharekhan


def form_tally_data_mcx(obj: Logit) -> Tuple[pd.DataFrame, pd.DataFrame]:
    mcx_netpos = pd.read_excel(
        config.PATH_TO_MCX_FILE,
        header=None,
        skiprows=3,
        usecols=[5, 6, 8, 9, 20],
        names=["symbol", "expiry", "strike", "type", "total_quantity"],
    )
    mcx_netpos["symbol"] = mcx_netpos["symbol"].str.lstrip("OF")
    mcx_netpos["expiry"] = pd.to_datetime(mcx_netpos["expiry"], format="%d/%m/%Y")
    mcx_netpos["expiry"] = mcx_netpos["expiry"].dt.strftime("%d-%b-%y")
    ncdex_netpos = pd.DataFrame()
    try:
        ncdex_netpos = pd.read_csv(
            config.PATH_TO_NCDEX_FILE,
            header=None,
            skiprows=1,
            usecols=[1, 2, 3, 4, 7],
            names=["symbol", "expiry", "strike", "type", "total_quantity"],
        )
        ncdex_netpos.total_quantity /= [
            obj.store_lot_size[x] * 0.1 for x in list(ncdex_netpos.symbol)
        ]
    except Exception as e:
        print("Skipping ncdex file!")

    mcx_netpos = pd.concat([mcx_netpos, ncdex_netpos])
    mcx_netpos.expiry = pd.to_datetime(mcx_netpos.expiry)
    mcx_netpos.expiry = mcx_netpos.expiry.apply(lambda x: x.strftime("%d-%b-%Y"))
    mcx_netpos["segment"] = [
        "FUTCOM" if pd.isna(i) else "OPTCOM" for i in list(mcx_netpos["type"])
    ]
    mcx_netpos["strike"] *= 100
    mcx_netpos["symbol"] = mcx_netpos.apply(
        lambda x: get_contract_name(
            x["segment"], x["symbol"], x["expiry"], x["type"], x["strike"]
        ),
        axis=1,
    )
    mcx_netpos["symbol"] = mcx_netpos["symbol"].apply(
        lambda x: x.rstrip("0").rstrip(".") if "." in x else x
    )

    mcx_netpos = mcx_netpos.drop(["strike", "type"], 1)
    combined_pos = mcx_netpos
    combined_pos = combined_pos.set_index(["symbol"]).groupby(level=0).sum()
    df_livecopy = obj.df_live.copy()
    df_livecopy["symbol"] = df_livecopy.apply(
        lambda x: get_contract_name(
            x["segment"], x["symbol"], x["expiry"], x["type"], x["strike"]
        ),
        axis=1,
    )
    df_livecopy["symbol"] = df_livecopy["symbol"].apply(
        lambda x: x.rstrip("0").rstrip(".") if "." in x else x
    )
    logit_pos = pd.DataFrame(df_livecopy.groupby("symbol")["total_quantity"].sum())
    return logit_pos, combined_pos


def tally_shareakhan_positions(
    _event: widgets, obj: Logit, out: widgets, flag_mcx: bool = False
):
    """Function to tally execution system positions.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
        flag_mcx (bool, optional): specifies we are trading in mcx. Defaults to False
    """
    with out:
        out.clear_output()
        try:
            if obj.mode == "NSE":
                logit_pos, sharekhan = form_tally_data(obj=obj)
                compare_df_with_quantities(
                    logit_pos, sharekhan, name1="_logit", name2="_sharekhan"
                )
            elif obj.mode == "COMM":
                logit_pos, combined_pos = form_tally_data_mcx(obj=obj)
                compare_df_with_quantities(
                    logit_pos, combined_pos, name1="_logit", name2="_exchange"
                )
        except Exception as e:
            display(e)


def add_sharekhan_mismatch_to_live_orders(
    _events: widgets, obj: Logit, hour: int, minute: int, out: widgets
):
    """Function to add execution system mismatch orders to live orders.

    Args:
        _events (widgets): trigger widget (Button)
        obj (Logit): logit object
        hour (int): hour for which ltp is to be fetched
        minute (int): minute for which ltp is to be fetched
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            logit_pos, sharekhan = form_tally_data(obj=obj)
            df_sk = compare_df_with_quantities(
                logit_pos,
                sharekhan,
                name1="_logit",
                name2="_sharekhan",
                return_value=True,
            )
            df_sk = df_sk.reset_index().rename(columns={"index": "symbol"})
            df_lastprice = get_ltp("FUTSTK", hour, minute)
            df_lastprice = df_lastprice.rename(
                columns={"contract": "symbol", "ltp": "close"}
            )

            df_error = create_pos_tally_error_df(df_sk, df_lastprice)
            obj.df_live = obj.df_live.append(df_error)
            obj.live = obj.df_live.to_dict("index")
            update_counts(obj=obj)
            update_df(obj=obj)
        except Exception as e:
            display(e)


def form_cash_positions(obj: Logit) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Function to form cash position tally data.

    Args:
        obj (Logit): logit object

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: logit position and total data
    """
    minio_file_downloader("execsystem", "cashnetposition_sk.txt", "temp.csv")
    cashnet = pd.read_csv(
        "temp.csv", header=None, usecols=[0, 7], names=["symbol", "total_quantity"]
    )
    minio_file_downloader("execsystem", "cashnetposition_greek.txt", "temp.csv")
    cashnet_greek = pd.read_csv(
        "temp.csv", header=None, usecols=[0, 6], names=["symbol", "total_quantity"]
    )
    total = pd.concat([cashnet, cashnet_greek])
    total = total.groupby("symbol").sum()
    logit_pos = obj.df_live[
        (obj.df_live["segment"] == "CASH")
        & (~obj.df_live["strategy"].isin(["error", "error_mft", "error_greek"]))
    ].copy()
    logit_pos = pd.DataFrame(logit_pos.groupby("symbol")["total_quantity"].sum())
    return logit_pos, total


def match_cash_positions(_events: widgets, obj: Logit, out: widgets):
    """Function to match cash positions.

    Args:
        _events (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            logit_pos, total = form_cash_positions(obj=obj)
            compare_df_with_quantities(
                logit_pos, total, name1="_logit", name2="_External"
            )
        except Exception as e:
            display(e)


def add_cash_mismatch_to_live_orders(
    _event: widgets, obj: Logit, hour: int, minute: int, out: widgets
):
    """Function to add cash mismatch to live orders.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        hour (int): hour for which ltp is to be fetched
        minute (int): minute for which ltp is to be fetched
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            df_lastprice = get_ltp("CASH", hour, minute)
            df_lastprice = df_lastprice.rename(
                columns={"contract": "symbol", "ltp": "close"}
            )
            logit_pos, total = form_cash_positions(obj=obj)
            df_sk = compare_df_with_quantities(
                logit_pos, total, name1="_logit", name2="_sharekhan", return_value=True
            )
            df_sk = df_sk.reset_index().rename(columns={"index": "symbol"})
            df_error = create_pos_tally_error_df(df_sk, df_lastprice)
            obj.df_live = obj.df_live.append(df_error)
            obj.live = obj.df_live.to_dict("index")
            update_counts(obj=obj)
            update_df(obj=obj)
        except Exception as e:
            display(e)


def reconcile_intraday_cashnet(_event: widgets, obj: Logit, out: widgets):
    """Function to reconcile intraday cashnet position.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            minio_file_downloader("execsystem", "cashnetposition_sk.txt", "temp.csv")
            cashnet = pd.read_csv(
                "temp.csv",
                header=None,
                usecols=[0, 7, 11, 12, 13, 15, 16],
                names=[
                    "Symbol",
                    "Net Qty",
                    "Bookedpnl",
                    "Buy Qty",
                    "Buy Rate",
                    "Sell Qty",
                    "Sell Rate",
                ],
            )
            cashnet["Bookedqty"] = cashnet[["Buy Qty", "Sell Qty"]].min(axis=1)
            cashnet["Bookedpnl"] /= 100
            cashnet["Buy Rate"] /= 100
            cashnet["Sell Rate"] /= 100
            df_dead_temp = obj.df_dead[obj.df_dead.segment == "CASH"].copy()
            df_dead_temp["pnl"] = df_dead_temp["total_quantity"] * (
                df_dead_temp["exit_price"] - df_dead_temp["entry_price"]
            )
            df_live_temp = obj.df_live[
                (obj.df_live.segment == "CASH")
                & (
                    ~(
                        obj.df_live["strategy"].isin(
                            ["error", "error_greek", "error_mft"]
                        )
                    )
                )
            ].copy()
            df_live_error = df_live_temp[df_live_temp.strategy == "notastrategy"]
            error_id = round(10 * (dt.datetime.now().timestamp()))
            i = 0
            while i < len(df_live_temp["symbol"]):
                temp_sym = df_live_temp["symbol"].iloc[i]
                cum_qty = df_live_temp.loc[
                    df_live_temp.symbol == temp_sym, "total_quantity"
                ].sum()
                temp_qty = int(
                    cashnet.loc[cashnet.Symbol == temp_sym, "Bookedqty"]
                    - cum_qty
                    - abs(
                        df_dead_temp.loc[
                            df_dead_temp.symbol == temp_sym, "total_quantity"
                        ]
                    ).sum()
                )
                net_qty = int(cashnet.loc[cashnet.Symbol == temp_sym, "Net Qty"])
                print(
                    "i={}, temp_sym={}, cum_qty={}, temp_qty={}, net_qty={}".format(
                        i, temp_sym, cum_qty, temp_qty, net_qty
                    )
                )
                if (
                    (temp_sym not in list(df_live_error.symbol))
                    & (temp_qty != 0)
                    & (cum_qty != net_qty)
                ):
                    if temp_qty > 0:
                        temp_price = float(
                            cashnet.loc[cashnet.Symbol == temp_sym, "Buy Rate"]
                        )
                    else:
                        temp_price = float(
                            cashnet.loc[cashnet.Symbol == temp_sym, "Sell Rate"]
                        )
                    df_live_error.loc[error_id] = [
                        dt.datetime.today(),
                        "CASH",
                        temp_sym,
                        "XX",
                        temp_qty,
                        "error_logging",
                        temp_price,
                        "XX",
                        0,
                        temp_price,
                    ]
                    error_id += 1
                i += 1
        except Exception as e:
            display(e)


def dead_sheet_cashnet(
    _event: widgets, obj: Logit, error_id: int, strategy_name: str, out: widgets
):
    """Function to form form dead sheet from cashnet.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        error_id (int): specified error id
        strategy_name (str): specified strategy name
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            ## Build live sheet from cashnet and exit
            start_time = dt.datetime(
                dt.datetime.now().year,
                dt.datetime.now().month,
                dt.datetime.now().day,
                9,
                30,
                1,
            )
            minio_file_downloader("execsystem", "cashnetposition_greek.txt", "temp.csv")
            cashnet_greek = pd.read_csv(
                "temp.csv",
                header=None,
                usecols=[0, 6, 7, 8, 9, 10, 13],
                names=[
                    "Symbol",
                    "Net Qty",
                    "Buy Qty",
                    "Sell Qty",
                    "Buy Rate",
                    "Sell Rate",
                    "Bookedpnl",
                ],
            )
            cashnet_greek["Bookedqty"] = cashnet_greek[["Buy Qty", "Sell Qty"]].min(
                axis=1
            )
            cashnet_greek = cashnet_greek[cashnet_greek.Bookedqty != 0]

            df_live_error = obj.df_live[obj.df_live.strategy == "notastrategy"]
            exitprice = []
            exitids = []
            i = 0
            while i < len(cashnet_greek["Symbol"]):
                while (error_id in obj.live) | (error_id in obj.dead):
                    error_id += 1
                temp_sym = cashnet_greek["Symbol"].iloc[i]
                temp_qty = cashnet_greek["Bookedqty"].iloc[i]
                temp_price = cashnet_greek["Buy Rate"].iloc[i]
                df_live_error.loc[error_id] = [
                    start_time,
                    "CASH",
                    temp_sym,
                    "XX",
                    temp_qty,
                    strategy_name,
                    temp_price,
                    "XX",
                    0,
                    temp_price,
                ]
                exitids.append(error_id)
                exitprice.append(cashnet_greek["Sell Rate"].iloc[i])
                error_id += 1
                i += 1
            df_live_error.to_csv("./csv/cashnet_gk.csv")

            obj.df_live = obj.df_live.append(df_live_error)
            obj.live = obj.df_live.to_dict("index")
            update_counts(obj=obj)
            update_df(obj=obj)
            for i, j in zip(exitids, exitprice):
                obj.store_sharekhan[i] = 1
                obj.exit_error_manually(i, j)  # TradeID , Exit Price
            update_counts(obj=obj)
            update_df(obj=obj)
        except Exception as e:
            display(e)


def exit_options_on_expiry_date(
    _event: widgets, obj: Logit, to_exit: str, nifty_spot: float, out: widgets
):
    """Function to exit options on expiry.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        to_exit (str): symbol to exit
        nifty_spot (float): specified spot price
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            if to_exit in obj.already_exitted:
                return
            exp = obj.logit_date.strftime("%d-%b-%Y")

            tmp = obj.df_live[
                (obj.df_live.symbol.isin([to_exit]))
                & (obj.df_live.expiry == exp)
                & (obj.df_live.type == "CE")
            ]
            trade_ids = list(tmp.index)
            strike = list(tmp["strike"].values)
            for trade_id, strike_ in zip(trade_ids, strike):
                if to_exit in config.OPTIDX_SYMBOL_LIST:
                    obj.store_sharekhan[trade_id] = 1
                obj.exit_error_manually(trade_id, max(nifty_spot - float(strike_), 0))
            tmp = obj.df_live[
                (obj.df_live.symbol.isin([to_exit]))
                & (obj.df_live.expiry == exp)
                & (obj.df_live.type == "PE")
            ]
            trade_ids = list(tmp.index)
            strike = list(tmp["strike"].values)
            for trade_id, strike_ in zip(trade_ids, strike):
                if to_exit in config.OPTIDX_SYMBOL_LIST:
                    obj.store_sharekhan[trade_id] = 1
                obj.exit_error_manually(trade_id, max(float(strike_) - nifty_spot, 0))
            update_counts(obj=obj)
            update_df(obj=obj)
            obj.already_exitted.add(to_exit)
        except Exception as e:
            display(e)


def apply_corporate_action(_event: widgets, obj: Logit, out: widgets):
    """Function to apply corporate action on specified symbol and segment.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            if obj.corpact_applied is True:
                return
            df_live = copy.deepcopy(obj.df_live)
            obj.df_live = apply_corporate_actions(df_live=df_live)
            obj.live = obj.df_live.to_dict(orient="index")
            update_df(obj=obj)
            update_counts(obj=obj)
            obj.corpact_applied = True
        except Exception as e:
            display(e)


def dump_csv(_event: widgets, obj: Logit, out: widgets, location: str):
    """Function to dump object to csv.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object to be dumped
        out (widgets): output widget
        location (str): location where we dump
    """
    with out:
        out.clear_output()
        try:
            if obj.mode in ["COMM"]:
                obj.dump_to_csv_mcx(location=location)
            elif obj.mode in ["KRX"]:
                obj.dump_to_csv_krx(location=location)
            elif obj.mode in ["US"]:
                obj.dump_to_csv_us(location=location)
            else:
                obj.dump_to_csv(location=location)
            display("Dumped to csv")
        except Exception as e:
            display(e)


def load_csv(_event: widgets, obj: Logit, out: widgets, location: str):
    """Function to load logit object from csv.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
        location (str): location from where we load
    """
    with out:
        out.clear_output()
        try:
            if obj.mode in ["COMM"]:
                obj.load_from_csv_mcx(location=location)
            elif obj.mode in ["KRX"]:
                obj.load_from_csv_krx(location=location)
            elif obj.mode in ["US"]:
                obj.load_from_csv_us(location=location)
            else:
                obj.load_from_csv(location=location)
            update_counts(obj=obj)
            update_df(obj=obj)
            display("Loaded from csv")
        except Exception as e:
            display(e)


def load_csv_df(
    _event: widgets, obj: Logit, out: widgets, identifier: str, location: str
):
    """Function to load logit object from csv.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
        identifier (str): specifies which df to load
        location (str): location from where we load
    """
    with out:
        out.clear_output()
        try:
            if obj.mode in ["COMM"]:
                obj.load_from_csv_mcx_df(identifier=identifier, location=location)
            elif obj.mode in ["KRX"]:
                obj.load_from_csv_krx_df(identifier=identifier, location=location)
            elif obj.mode in ["US"]:
                obj.load_from_csv_us_df(identifier=identifier, location=location)
            else:
                obj.load_from_csv_df(identifier=identifier, location=location)
            update_counts(obj=obj)
            update_df(obj=obj)
            display("Loaded from csv")
        except Exception as e:
            display(e)


def form_curr_data(obj: Logit):
    """Function to form currency data.

    Args:
        obj (Logit): logit object
    """
    try:
        minio_file_downloader(
            "commondata", "nse_daily_downloads/cd_fo_bhav.csv", "curfobhav.txt"
        )
        df_bhav = pd.read_csv("curfobhav.txt")
        df_mtm = pd.read_sql(
            f"select * from {config.CURRENCY_LEDGER} ORDER BY mtm_date ASC",
            con=obj.portfolio_stats_engine,
        )

        [running_mtm, inout_flow] = get_running_mtm_cur(obj.live, df_bhav)
        booked_pnl_yesterday = df_mtm["daypnl"].sum()

        date = input(
            "Date for mtm, use date of last trading day in YYYY-MM-DD format : "
        )
        date = parser.parse(date).date()

        gap_yesterday = df_mtm["mtm_gap"].iloc[-1]
        pnl_today = get_today_pnl(obj.dead, cur_flag=True)
        print("PL Balance in Results Sheet: " + str(booked_pnl_yesterday + pnl_today))
        print("Running MTM on current Positions : " + str(running_mtm))

        sk_balance = str2num(input("Sharekhan Ledger Balance: "))
        kc_adjust = str2num(
            input("Credit(+)/Debit(-) from Sharekhan ? leave empty if none: ")
        )

        gap = (
            kc_adjust
            + df_mtm["cur_adjust"].sum()
            + df_mtm["kc_reserved3"].sum()
            + pnl_today
            + booked_pnl_yesterday
            + running_mtm
            + inout_flow
            - sk_balance
        )
        print("Gap in MTM as reported by external agents and us: " + str(gap))
        print("Corresponding Gap yesterday : " + str(gap_yesterday))

        kc_reserved3 = str2num(
            input("Credit/Debit(-) gap adjustment ? leave empty if none: ")
        )
        if kc_reserved3 != 0:
            gap = gap + kc_reserved3
            print("New Gap in MTM as reported by external agents and us: " + str(gap))

        display("Update DB if the numbers look alright.")
        update_db_input = input(
            "Do you want to update the DB? Press y or Y to confirm."
        )
        if update_db_input.upper() == "Y":
            query = (
                "INSERT INTO curledger (mtm_date, sharekhancurbalance, cashinflow, daypnl, mtmtoday, cur_adjust, kc_reserved3, mtm_gap) VALUES ('"
                + date.strftime("%Y-%m-%d")
                + "'"
            )
            values = [
                sk_balance,
                inout_flow,
                pnl_today,
                running_mtm,
                kc_adjust,
                kc_reserved3,
                gap,
            ]
            for v in values:
                query = query + "," + str(v)
            query = query + ")"
            obj.portfolio_stats_engine.execute(query)
            display("MTM Cur Database has been Updated")
        clear_dead_sheet_input = input(
            "Do you want to clear the dead sheet from logit object? Press y or Y to confirm."
        )
        if clear_dead_sheet_input.upper() == "Y":
            obj.dead = {}
            update_counts(obj=obj)
            update_df(obj=obj)
    except Exception as e:
        display(e)


def form_morning_varaibles(obj: Logit):
    """Function to form morning variables.

    Args:
        obj (Logit): logit object
    """
    try:

        def get_netobligation(date1):

            file_format = ".csv.gz"
            sd_fo = stt_fo = payin_payout_fo = 0
            sd_cash = stt_cash = payin_payout_cash = 0

            last_trading_date = dt.datetime.strftime(date1, "%Y%m%d")
            # file_name = "F_SD01_90114_" + last_trading_date + file_format
            file_name = (
                "StampDuty_NCL_FO_0_CM_90114_"
                + last_trading_date
                + "_F_0000"
                + file_format
            )
            path = config.reports_bucket + file_name
            try:
                file = BytesIO(
                    config.minioClient.get_object(config.BUCKET_NAME, path).data
                )
                with gzip.open(file, "rb") as f:
                    tmp_line = f.readline()
                    tmp_line1 = f.readline()
                sd_fo = float(tmp_line1.decode("utf-8").rstrip().split(",")[32])
            except Exception:
                print("{} file does not exist.".format(file_name))

            # file_name = "F_STT01_90114_" + last_trading_date + file_format
            file_name = (
                "STT_NCL_FO_0_CM_90114_" + last_trading_date + "_F_0000" + file_format
            )

            path = config.reports_bucket + file_name
            try:
                file = BytesIO(
                    config.minioClient.get_object(config.BUCKET_NAME, path).data
                )
                with gzip.open(file, "rb") as f:
                    tmp_line = f.readline()
                    tmp_line1 = f.readline()
                stt_fo = float(tmp_line1.decode("utf-8").rstrip().split(",")[43])
            except Exception:
                print("{} file does not exist.".format(file_name))

            last_trading_date = pd.Timestamp(last_trading_date).strftime("%d%m%Y")
            try:
                file_name = (
                    "F_BK01_90114_"
                    + dt.datetime.today().strftime("%d%m%Y")
                    + "_"
                    + (dt.datetime.strptime(last_trading_date, "%d%m%Y").strftime("%d"))
                    + ".CSV.gz"
                )
                path = config.reports_bucket + file_name
                file = BytesIO(
                    config.minioClient.get_object(config.BUCKET_NAME, path).data
                )
                with gzip.open(file, "rb") as f:
                    tmp_line = pd.read_csv(f)
                if tmp_line["Debit Amount"][0] == 0:
                    payin_payout_fo = float(tmp_line["Credit Amount"][0])
                else:
                    payin_payout_fo = -1 * float(tmp_line["Debit Amount"][0])
            except Exception:
                print("{} file does not exist.".format(file_name))
                payin_payout_fo = str2num(input("Enter payin_payout_fo manually: "))

            try:
                file_name = "C_SD01_90114_" + last_trading_date + ".csv"
                path = config.reports_bucket + file_name
                file = BytesIO(
                    config.minioClient.get_object(config.BUCKET_NAME, path).data
                )
                with open(file, "r") as f:
                    tmp_line = f.readline()
                sd_cash = float(tmp_line.rstrip().split(",")[-1])
            except Exception:
                print("{} file does not exist.".format(file_name))

            try:
                file_name = "MWST_90114_" + last_trading_date + ".csv"
                path = config.reports_bucket + file_name
                file = BytesIO(
                    config.minioClient.get_object(config.BUCKET_NAME, path).data
                )
                with open(file, "r") as f:
                    tmp_line = f.readline()
                stt_cash = float(tmp_line.rstrip().split(",")[-1])
            except Exception:
                print("{} file does not exist.".format(file_name))

            try:
                file_name = "C_90114_NDOBG_N20*_" + last_trading_date + ".csv.gz"
                path = config.reports_bucket + file_name
                file = BytesIO(
                    config.minioClient.get_object(config.BUCKET_NAME, path).data
                )
                with gzip.open(file, "rb") as f:
                    tmp_line = f.read().splitlines()
                    tmp_line = tmp_line[-1]
                payin_payout_cash = float(
                    tmp_line.decode("utf-8").rstrip().split(",")[-1]
                )
            except Exception:
                print("Either multiple files or no file")
                print(path)

            return (
                sd_cash,
                stt_cash,
                payin_payout_cash,
                sd_fo,
                stt_fo,
                payin_payout_fo,
            )

        df_mtm = pd.read_sql(
            f"select * from {config.EQ_LEDGER} ORDER BY mtm_date ASC",
            con=obj.portfolio_stats_engine,
        )

        minio_file_downloader(
            "commondata", "nse_daily_downloads/fo_bhav.csv", "./csv/bhav_fo.csv"
        )
        bhav_fo = pd.read_csv("./csv/bhav_fo.csv", usecols=[1, 2, 3, 4, 8])
        bhav_fo.EXPIRY_DT = pd.to_datetime(bhav_fo.EXPIRY_DT)
        bhav_fo.EXPIRY_DT = bhav_fo.EXPIRY_DT.apply(lambda x: x.strftime("%d-%b-%Y"))
        minio_file_downloader(
            "commondata", "bse_daily_downloads/bse_fo_bhav.csv", "./csv/bse_bhav_fo.csv"
        )
        bse_bhav_fo = pd.read_csv("./csv/bse_bhav_fo.csv", usecols=[1, 2, 3, 4, 8])
        bse_bhav_fo.EXPIRY_DT = pd.to_datetime(bse_bhav_fo.EXPIRY_DT)
        bse_bhav_fo.EXPIRY_DT = bse_bhav_fo.EXPIRY_DT.apply(
            lambda x: x.strftime("%d-%b-%Y")
        )
        bhav_fo = pd.concat([bhav_fo, bse_bhav_fo])
        [running_mtm, inout_flow] = get_running_mtm(obj.live, bhav_fo)
        booked_pnl_yesterday = df_mtm["daypnl"].sum()
        date = input(
            "Date for mtm , use date of last trading day in YYYY-MM-DD format : "
        )
        date = parser.parse(date).date()

        [
            cash_sd,
            cash_stt,
            cash_payinout,
            fo_sd,
            fo_stt,
            fo_payinout,
        ] = get_netobligation(date)
        print(
            "sd_fo: {}, stt_fo: {}, payin_payout_fo: {}, and net_inout_fo: {}".format(
                fo_sd, fo_stt, fo_payinout, fo_payinout - fo_sd - fo_stt
            )
        )
        print(
            "sd_cash: {}, stt_csah: {}, payin_payout_cash: {}, and net_inout_cash: {}".format(
                cash_sd, cash_stt, cash_payinout, cash_payinout - cash_sd - cash_stt
            )
        )
        cash_payinout += str2num(
            input(
                "Debit(-) auction value for short error from Kivi Securities? leave empty if none: "
            )
        )

        gap_yesterday = 0
        if len(df_mtm) > 0:
            gap_yesterday = df_mtm["mtm_gap"].iloc[-1]
        pnl_today = get_today_pnl(obj.dead)
        print("PNL value for today's deads : ", pnl_today)
        print("PL Balance in Results Sheet: " + str(booked_pnl_yesterday + pnl_today))
        print("Running MTM on current Positions : " + str(running_mtm))
        sk_balance = str2num(input("Sharekhan Ledger Balance: "))
        kc_adjust = str2num(
            input("Credit(+)/Debit(-) from Sharekhan ? leave empty if none: ")
        )
        ks_adjust = str2num(
            input(
                "Credit(+)/Debit(-) from Kivi Securities CASH Settlement account? leave empty if none: "
            )
        )
        ks_reserved1 = str2num(
            input(
                "Credit/Debit(-) from Kivi Securities FO Settlement account? leave empty if none: "
            )
        )

        ks_cash_outflow = 0
        ks_cash_outflow = cash_payinout - cash_sd - cash_stt
        ks_cash_outflow = ks_cash_outflow + df_mtm["kiviseccash"].iloc[-1] + ks_adjust
        print("Kivisec CASH ledger balance: " + str(ks_cash_outflow))
        gc_balance = 0
        gc_balance = fo_payinout - fo_sd - fo_stt
        gc_balance = gc_balance + df_mtm["kivisecbalance"].iloc[-1] + ks_reserved1
        print("Kivisec FO ledger balance: " + str(gc_balance))

        kc_reserved2 = str2num(input("Kivisec monthly charges ? leave empty if none: "))
        gap = (
            kc_adjust
            + df_mtm["kivicapadjust"].sum()
            + ks_adjust
            + df_mtm["kivisecadjust"].sum()
            + ks_reserved1
            + df_mtm["ks_reserved1"].sum()
            + df_mtm["ks_reserved2"].sum()
            + kc_reserved2
            + df_mtm["kc_reserved2"].sum()
            + pnl_today
            + booked_pnl_yesterday
            + running_mtm
            + inout_flow
            - ks_cash_outflow
            - sk_balance
            - gc_balance
        )

        print("Gap in MTM as reported by external agents and us: " + str(gap))
        print("Corresponding Gap yesterday : " + str(gap_yesterday))

        ks_reserved2 = str2num(
            input("Credit/Debit(-) gap adjustment ? leave empty if none: ")
        )
        if ks_reserved2 != 0:
            gap = gap + ks_reserved2
            print("New Gap in MTM as reported by external agents and us: " + str(gap))

        display("Update DB if the numbers look alright")
        update_db_input = input(
            "Do you want to update the DB? Press y or Y to confirm."
        )
        if update_db_input.upper() == "Y":
            query = (
                "INSERT INTO eqledger (mtm_date,sharekhanbalance, kivisecbalance, kiviseccash,cashinflow,daypnl, mtmtoday, kivicapadjust, kivisecadjust,ks_reserved1,mtm_gap,ks_reserved2,kc_reserved2) VALUES ('"
                + date.strftime("%Y-%m-%d")
                + "'"
            )
            values = [
                sk_balance,
                gc_balance,
                ks_cash_outflow,
                inout_flow,
                pnl_today,
                running_mtm,
                kc_adjust,
                ks_adjust,
                ks_reserved1,
                gap,
                ks_reserved2,
                kc_reserved2,
            ]
            for v in values:
                query = query + "," + str(v)
            query = query + ")"
            obj.portfolio_stats_engine.execute(query)
            display("MTM Database has been Updated")
        clear_dead_sheet_input = input(
            "Do you want to clear the dead sheet from logit object? Press y or Y to confirm."
        )
        if clear_dead_sheet_input.upper() == "Y":
            obj.dead = {}
            update_counts(obj=obj)
            update_df(obj=obj)
    except Exception as e:
        display(e)


def eod_checks(_event: widgets, obj: Logit, out: widgets):
    """Function to perform eod checks.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            today = dt.datetime.strptime(str(obj.logit_date), "%Y-%m-%d").strftime(
                "%Y%m%d"
            )
            ALL_DATES = (
                config.ALL_DATES_NSE
                if obj.mode == "NSE"
                else (
                    config.ALL_DATES_MCX
                    if obj.mode == "MCX"
                    else (
                        config.ALL_DATES_GIFT
                        if obj.mode == "GIFT"
                        else (
                            config.ALL_DATES_KRX
                            if obj.mode == "KRX"
                            else config.ALL_DATES_US
                        )
                    )
                )
            )
            previous_working_day = previous_date(
                ALL_DATES=ALL_DATES, date=pd.Timestamp(str(obj.logit_date)), lookback=1
            ).date()
            previous_working_day = dt.datetime.strptime(
                str(previous_working_day), "%Y-%m-%d"
            ).strftime("%Y%m%d")
            day_before_yesterday = previous_date(
                ALL_DATES=ALL_DATES, date=pd.Timestamp(str(obj.logit_date)), lookback=2
            ).date()
            day_before_yesterday = dt.datetime.strptime(
                str(day_before_yesterday), "%Y-%m-%d"
            ).strftime("%Y%m%d")
            day_before_yesterday_live_sheet = (
                "live_sheet_" + obj.mode + "_" + str(day_before_yesterday) + ".parquet"
            )
            if obj.mode == "NSE":

                def combine_turnover(greek_turnover, sk_turnover):
                    combined_tmp = pd.concat(
                        [greek_turnover, sk_turnover], ignore_index=True, sort=False
                    )
                    combined_tmp["buyval"] = (
                        combined_tmp.intradayBuyQty * combined_tmp.todayAvgBuyPrice
                    )
                    combined_tmp["sellval"] = (
                        combined_tmp.intradaySellQty * combined_tmp.todayAvgSellPrice
                    )
                    combined_to = pd.DataFrame()
                    combined_to["symbol"] = list(set(list(combined_tmp.symbol)))
                    combined_to["totalNetQty"] = combined_to.symbol.apply(
                        lambda x: (
                            combined_tmp.groupby("symbol")["totalNetQty"].sum().loc[x]
                            if x in list(combined_tmp.symbol)
                            else 0
                        )
                    )
                    combined_to["intradayBuyQty"] = combined_to.symbol.apply(
                        lambda x: (
                            combined_tmp.groupby("symbol")["intradayBuyQty"]
                            .sum()
                            .loc[x]
                            if x in list(combined_tmp.symbol)
                            else 0
                        )
                    )
                    combined_to["intradaySellQty"] = combined_to.symbol.apply(
                        lambda x: (
                            combined_tmp.groupby("symbol")["intradaySellQty"]
                            .sum()
                            .loc[x]
                            if x in list(combined_tmp.symbol)
                            else 0
                        )
                    )
                    combined_to["todayAvgBuyPrice"] = combined_to.symbol.apply(
                        lambda x: (
                            combined_tmp.groupby("symbol")["buyval"].sum().loc[x]
                            if x in list(combined_tmp.symbol)
                            else 0
                        )
                    )
                    combined_to["todayAvgBuyPrice"] /= combined_to["intradayBuyQty"]
                    combined_to["todayAvgSellPrice"] = combined_to.symbol.apply(
                        lambda x: (
                            combined_tmp.groupby("symbol")["sellval"].sum().loc[x]
                            if x in list(combined_tmp.symbol)
                            else 0
                        )
                    )
                    combined_to["todayAvgSellPrice"] /= combined_to["intradaySellQty"]
                    combined_to = combined_to.fillna(0)
                    return combined_to

                # This code will compare mtm in logit with mtm in turnover/cashnet report
                df_deadcopy = obj.df_dead.copy()
                df_livecopy = obj.df_live.copy()
                df_liveprev = fetch_df_from_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"live_sheet_NSE_{previous_working_day}.parquet",
                )
                df_liveprev.rename(
                    columns={
                        "entry_price": "weighted_price",
                        "entry_timestamp": "timestamp",
                    },
                    inplace=True,
                )
                df_deadcopy["pnl"] = df_deadcopy.total_quantity * (
                    df_deadcopy.exit_price - df_deadcopy.entry_price
                )
                df_deadcopy["contract"] = df_deadcopy.apply(
                    lambda x: get_contract_name(
                        x["segment"], x["symbol"], x["expiry"], x["type"], x["strike"]
                    ),
                    axis=1,
                )
                df_deadcopy["contract"] = df_deadcopy["contract"].apply(
                    lambda x: x.rstrip("0").rstrip(".") if "." in x else x
                )
                df_livecopy["contract"] = df_livecopy.apply(
                    lambda x: get_contract_name(
                        x["segment"], x["symbol"], x["expiry"], x["type"], x["strike"]
                    ),
                    axis=1,
                )
                df_livecopy["contract"] = df_livecopy["contract"].apply(
                    lambda x: x.rstrip("0").rstrip(".") if "." in x else x
                )
                df_liveprev["contract"] = df_liveprev.apply(
                    lambda x: get_contract_name(
                        x["segment"], x["symbol"], x["expiry"], x["type"], x["strike"]
                    ),
                    axis=1,
                )
                df_liveprev["contract"] = df_liveprev["contract"].apply(
                    lambda x: x.rstrip("0").rstrip(".") if "." in x else x
                )
                df_livecopy["payinout"] = (
                    -1 * df_livecopy.total_quantity * df_livecopy.weighted_price
                )
                df_liveprev["payinout"] = (
                    -1 * df_liveprev.total_quantity * df_liveprev.weighted_price
                )
                df_segment = df_livecopy[["contract", "segment"]]
                df_segment = pd.concat(
                    [df_segment, df_liveprev[["contract", "segment"]]],
                    ignore_index=True,
                )
                df_segment = pd.concat(
                    [df_segment, df_deadcopy[["contract", "segment"]]],
                    ignore_index=True,
                )
                df_segment = df_segment.drop_duplicates(subset="contract")
                df_segment = df_segment.set_index("contract")["segment"].to_dict()
                minio_file_downloader("execsystem", "turnover_greek.txt", "temp.csv")
                greek_turnover = pd.read_csv(
                    "temp.csv",
                    header=None,
                    usecols=[0, 2, 6, 7, 8, 9],
                    names=[
                        "symbol",
                        "totalNetQty",
                        "intradayBuyQty",
                        "intradaySellQty",
                        "todayAvgBuyPrice",
                        "todayAvgSellPrice",
                    ],
                )
                minio_file_downloader("execsystem", "turnover_sk.txt", "temp.csv")
                sk_turnover = pd.read_csv(
                    "temp.csv",
                    header=None,
                    usecols=[0, 2, 7, 8, 9, 11, 12],
                    names=[
                        "symbol",
                        "segment",
                        "intradayBuyQty",
                        "intradaySellQty",
                        "totalNetQty",
                        "todayAvgBuyPrice",
                        "todayAvgSellPrice",
                    ],
                )
                sk_turnover = sk_turnover.loc[
                    sk_turnover["segment"] == "NF"
                ].reset_index(drop=True)
                sk_turnover = sk_turnover.drop(sk_turnover.columns[[1]], axis=1)
                sk_turnover["intradaySellQty"] = sk_turnover["intradaySellQty"] * -1
                sk_turnover["todayAvgBuyPrice"] = sk_turnover["todayAvgBuyPrice"] / 100
                sk_turnover["todayAvgSellPrice"] = (
                    sk_turnover["todayAvgSellPrice"] / 100
                )
                greek_turnover = combine_turnover(greek_turnover, sk_turnover)
                minio_file_downloader(
                    "execsystem", "cashnetposition_sk.txt", "temp.csv"
                )
                sk_cashnet = pd.read_csv(
                    "temp.csv",
                    header=None,
                    usecols=[0, 7, 12, 14, 15, 17],
                    names=[
                        "symbol",
                        "totalNetQty",
                        "intradayBuyQty",
                        "intradayBuyVal",
                        "intradaySellQty",
                        "intradaySellVal",
                    ],
                )
                sk_cashnet["todayAvgBuyPrice"] = sk_cashnet["intradayBuyVal"] / (
                    100 * sk_cashnet["intradayBuyQty"]
                )
                sk_cashnet["todayAvgSellPrice"] = sk_cashnet["intradaySellVal"] / (
                    100 * sk_cashnet["intradaySellQty"]
                )
                sk_cashnet = sk_cashnet.drop(
                    ["intradayBuyVal", "intradaySellVal"], axis=1
                )
                greek_turnover = combine_turnover(greek_turnover, sk_cashnet)
                greek_turnover["buyval"] = (
                    greek_turnover.intradayBuyQty * greek_turnover.todayAvgBuyPrice
                )
                greek_turnover["sellval"] = (
                    greek_turnover.intradaySellQty * greek_turnover.todayAvgSellPrice
                )
                greek_turnover["netpay"] = (
                    greek_turnover.sellval - greek_turnover.buyval
                )
                greek_turnover["deadnetpay"] = greek_turnover.symbol.apply(
                    lambda x: (
                        df_deadcopy.groupby("contract")["pnl"].sum().loc[x]
                        if x in list(df_deadcopy.contract)
                        else 0
                    )
                )
                greek_turnover["livenetpay"] = greek_turnover.symbol.apply(
                    lambda x: (
                        df_livecopy.groupby("contract")["payinout"].sum().loc[x]
                        if x in list(df_livecopy.contract)
                        else 0
                    )
                )
                greek_turnover["plivenetpay"] = greek_turnover.symbol.apply(
                    lambda x: (
                        df_liveprev.groupby("contract")["payinout"].sum().loc[x]
                        if x in list(df_liveprev.contract)
                        else 0
                    )
                )
                greek_turnover["neterror"] = (
                    greek_turnover.deadnetpay
                    + greek_turnover.livenetpay
                    - greek_turnover.plivenetpay
                ) - greek_turnover.netpay
                greek_turnover["abserror"] = abs(greek_turnover.neterror)
                segment_tmp = greek_turnover.symbol.apply(
                    lambda x: df_segment[x] if x in list(df_segment.keys()) else "XX"
                )
                greek_turnover.insert(1, "segment", segment_tmp, True)
                greek_turnover.to_csv("./csv/contractwiseerror.csv", index=False)
                print(
                    "Gap between logit and netposition for CASH is : {} ".format(
                        round(
                            greek_turnover[
                                greek_turnover.segment == "CASH"
                            ].neterror.sum()
                        )
                    )
                )
                print(
                    "Gap between logit and turnover for FUTSTK and FUTIDX is : {} ".format(
                        round(
                            greek_turnover[
                                greek_turnover.segment.isin(["FUTSTK", "FUTIDX"])
                            ].neterror.sum()
                        )
                    )
                )
                print(
                    "Gap between logit and turnover for OPTSTK and OPTIDXis : {} ".format(
                        round(
                            greek_turnover[
                                greek_turnover.segment.isin(
                                    config.OPTIONS_UNIVERSE_MAPPING["NSE"]
                                )
                            ].neterror.sum()
                        )
                    )
                )
                # Below code will check long short
                minio_file_downloader("execsystem", "turnover_sk.txt", "temp.csv")
                sharekhan = pd.read_csv(
                    "temp.csv",
                    header=None,
                    usecols=[0, 9],
                    names=["symbol", "total_quantity"],
                )
                minio_file_downloader("execsystem", "turnover_greek.txt", "temp.csv")
                greek_turnover = pd.read_csv(
                    "temp.csv",
                    header=None,
                    usecols=[0, 2],
                    names=["symbol", "total_quantity"],
                )
                symbol = list(
                    pd.concat(
                        [sharekhan, greek_turnover], ignore_index=True
                    ).symbol.unique()
                )
                df_turnover_tally = pd.DataFrame(symbol, columns=["symbol"])
                df_turnover_tally = df_turnover_tally.merge(
                    sharekhan, on="symbol", how="left"
                )
                df_turnover_tally = df_turnover_tally.rename(
                    columns={"total_quantity": "sharekhan"}
                )
                df_turnover_tally = df_turnover_tally.merge(
                    greek_turnover, on="symbol", how="left"
                )
                df_turnover_tally = df_turnover_tally.rename(
                    columns={"total_quantity": "greek"}
                )
                print(
                    df_turnover_tally[
                        (
                            (df_turnover_tally.sharekhan > 0)
                            & (df_turnover_tally.greek < 0)
                        )
                        | (
                            (df_turnover_tally.sharekhan < 0)
                            & (df_turnover_tally.greek > 0)
                        )
                    ].to_string(index=False)
                )
                obj.dump_to_csv()
                live = obj.df_live.copy()
                live["strike"] = live["strike"].astype(np.double)
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"live_sheet_{obj.mode}_{today}.parquet",
                    df=live,
                )
                display("today's live sheet uploaded to minio")
                objects = config.minioClient.list_objects(config.LIVE_DEAD_SHEET_BUCKET)
                if day_before_yesterday_live_sheet in [
                    obj.object_name for obj in objects
                ]:
                    config.minioClient.remove_object(
                        bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                        object_name=day_before_yesterday_live_sheet,
                    )
                    display("day before yesterday's live sheet removed")
                else:
                    display("day before yesterday's live sheet not found")
                dead_sheet = fetch_df_from_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                )
                dead = obj.df_dead.copy()
                dead["strike"] = dead["strike"].astype(np.double)
                dead_sheet = pd.concat([dead_sheet, dead])
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                    df=dead_sheet,
                )
                display("dead sheet uploaded to minio")
            elif obj.mode == "COMM":
                obj.dump_to_csv_mcx()
                live = obj.df_live.copy()
                live["strike"] = live["strike"].astype(np.double)
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"live_sheet_{obj.mode}_{today}.parquet",
                    df=live,
                )
                display("today's live sheet uploaded to minio")
                objects = config.minioClient.list_objects(config.LIVE_DEAD_SHEET_BUCKET)
                if day_before_yesterday_live_sheet in [
                    obj.object_name for obj in objects
                ]:
                    config.minioClient.remove_object(
                        bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                        object_name=day_before_yesterday_live_sheet,
                    )
                    display("day before yesterday's live sheet removed")
                else:
                    display("day before yesterday's live sheet not found")
                dead_sheet = fetch_df_from_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                )
                dead = obj.df_dead.copy()
                dead["strike"] = dead["strike"].astype(np.double)
                dead_sheet = pd.concat([dead_sheet, dead])
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                    df=dead_sheet,
                )
                display("dead sheet uploaded to minio")
            elif obj.mode == "GIFT":
                obj.dump_to_csv_gift()
                live = obj.df_live.copy()
                if len(live):
                    live["strike"] = live["strike"].astype(np.double)
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"live_sheet_{obj.mode}_{today}.parquet",
                    df=live,
                )
                display("today's live sheet uploaded to minio")
                objects = config.minioClient.list_objects(config.LIVE_DEAD_SHEET_BUCKET)
                if day_before_yesterday_live_sheet in [
                    obj.object_name for obj in objects
                ]:
                    config.minioClient.remove_object(
                        bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                        object_name=day_before_yesterday_live_sheet,
                    )
                    display("day before yesterday's live sheet removed")
                else:
                    display("day before yesterday's live sheet not found")
                dead_sheet = fetch_df_from_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                )
                dead = obj.df_dead.copy()
                if len(dead):
                    dead["strike"] = dead["strike"].astype(np.double)
                dead_sheet = pd.concat([dead_sheet, dead])
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                    df=dead_sheet,
                )
                display("dead sheet uploaded to minio")
            elif obj.mode == "KRX":
                df_ledger: pd.DataFrame = pd.read_sql(
                    "SELECT * FROM krxledger", con=obj.portfolio_stats_engine
                )
                if (
                    previous_date(config.ALL_DATES_KRX, obj.logit_date, 1).date()
                    not in df_ledger.mtm_date.to_list()
                ):
                    raise Exception(
                        "EOD Process cannot be performed until ledger for last day has been done!"
                    )
                obj.dump_to_csv_krx()
                live = obj.df_live.copy()
                if len(live):
                    live["strike"] = live["strike"].astype(np.double)
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"live_sheet_{obj.mode}_{today}.parquet",
                    df=live,
                )
                display("today's live sheet uploaded to minio")
                objects = config.minioClient.list_objects(config.LIVE_DEAD_SHEET_BUCKET)
                if day_before_yesterday_live_sheet in [
                    obj.object_name for obj in objects
                ]:
                    config.minioClient.remove_object(
                        bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                        object_name=day_before_yesterday_live_sheet,
                    )
                    display("day before yesterday's live sheet removed")
                else:
                    display("day before yesterday's live sheet not found")
                dead_sheet = fetch_df_from_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                )
                dead = obj.df_dead.copy()
                if len(dead):
                    dead["strike"] = dead["strike"].astype(np.double)
                dead_sheet = pd.concat([dead_sheet, dead])
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                    df=dead_sheet,
                )
                display("dead sheet uploaded to minio")
            elif obj.mode == "US":
                df_ledger: pd.DataFrame = pd.read_sql(
                    "SELECT * FROM usledger", con=obj.portfolio_stats_engine
                )
                if (
                    previous_date(config.ALL_DATES_US, obj.logit_date, 1).date()
                    not in df_ledger.mtm_date.to_list()
                ):
                    raise Exception(
                        "EOD Process cannot be performed until ledger for last day has been done!"
                    )
                obj.dump_to_csv_us()
                live = obj.df_live.copy()
                if len(live):
                    live["strike"] = live["strike"].astype(np.double)
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"live_sheet_{obj.mode}_{today}.parquet",
                    df=live,
                )
                display("today's live sheet uploaded to minio")
                objects = config.minioClient.list_objects(config.LIVE_DEAD_SHEET_BUCKET)
                if day_before_yesterday_live_sheet in [
                    obj.object_name for obj in objects
                ]:
                    config.minioClient.remove_object(
                        bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                        object_name=day_before_yesterday_live_sheet,
                    )
                    display("day before yesterday's live sheet removed")
                else:
                    display("day before yesterday's live sheet not found")
                dead_sheet = fetch_df_from_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                )
                dead = obj.df_dead.copy()
                if len(dead):
                    dead["strike"] = dead["strike"].astype(np.double)
                dead_sheet = pd.concat([dead_sheet, dead])
                upload_df_to_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{obj.mode}.parquet",
                    df=dead_sheet,
                )
                display("dead sheet uploaded to minio")
        except Exception as e:
            display(e)


def day_start_comm(obj: Logit):
    """Function to initialise commodities morning variables.

    Args:
        obj (Logit): logit object
    """
    try:
        df_mtm = pd.read_sql(
            f"select * from {config.COMM_LEDGER} ORDER BY mtm_date ASC",
            con=obj.portfolio_stats_engine,
        )

        minio_file_downloader(
            "commondata",
            "ncdex_daily_downloads/ncdex_bhavcopy.csv",
            "./csv/bhav_ncdex.csv",
        )
        bhav_ncdex = pd.read_csv(
            "./csv/bhav_ncdex.csv",
            header=None,
            usecols=[1, 2, 3, 23],
            names=["segment", "symbol", "expiry", "close"],
        )
        bhav_ncdex = bhav_ncdex[bhav_ncdex["segment"].isin(["FUTCOM", "FUTIDX"])]
        bhav_ncdex.expiry = pd.to_datetime(bhav_ncdex.expiry)

        minio_file_downloader(
            "commondata", "mcx_daily_downloads/mcx_bhav.csv", "./csv/bhav_mcx.csv"
        )
        bhav_mcx = pd.read_csv("./csv/bhav_mcx.csv", usecols=[1, 2, 3, 9])
        bhav_mcx = bhav_mcx.rename(
            columns={
                "Instrument Name": "segment",
                "Symbol": "symbol",
                "Expiry Date": "expiry",
                "Close": "close",
            }
        )
        bhav_mcx = bhav_mcx[bhav_mcx["segment"].isin(["FUTCOM", "FUTIDX"])]
        bhav_mcx.expiry = pd.to_datetime(bhav_mcx.expiry)

        bhav_comm = pd.concat([bhav_mcx, bhav_ncdex])
        bhav_comm.expiry = bhav_comm.expiry.apply(lambda x: x.strftime("%d-%b-%Y"))

        [running_mtm, inout_flow] = get_running_mtm_comm(
            obj.live, bhav_comm, obj.store_lot_size
        )
        booked_pnl_yesterday = df_mtm["daypnl"].sum()
        date = input(
            "Date for mtm , use date of last trading day in YYYY-MM-DD format : "
        )
        date = parser.parse(date).date()
        gap_yesterday = 0
        if len(df_mtm) > 0:
            gap_yesterday = df_mtm["mtm_gap"].iloc[-1]
        pnl_today = get_today_pnl_comm(obj.dead, obj.store_lot_size)
        print("PNL value for today's deads : ", pnl_today)  # Show PnL for DEAD
        print("PL Balance in Results Sheet: " + str(booked_pnl_yesterday + pnl_today))
        print("Running MTM on current Positions : " + str(running_mtm))
        sk_balance = str2num(input("MCX Ledger Balance on Globe : "))
        kc_adjust = str2num(
            input("Credit(+)/Debit(-) from MCX Ledger ? leave empty if none: ")
        )
        gc_balance = str2num(input("NCDEX Ledger Balance: "))
        ks_adjust = str2num(
            input("Credit(+)/Debit(-) from NCDEX Ledger ? leave empty if none: ")
        )
        gap = (
            kc_adjust
            + df_mtm["sharekhan_adjust"].sum()
            + ks_adjust
            + df_mtm["mastertrust_adjust"].sum()
            + df_mtm["reserved4"].sum()
            + pnl_today
            + booked_pnl_yesterday
            + running_mtm
            + inout_flow
            - sk_balance
            - gc_balance
        )
        print("Gap in MTM as reported by external agents and us: " + str(gap))
        print("Corresponding Gap yesterday : " + str(gap_yesterday))
        reserved4 = str2num(
            input("Credit/Debit(-) gap adjustment ? leave empty if none: ")
        )
        if reserved4 != 0:
            gap = gap + reserved4
            print("New Gap in MTM as reported by external agents and us: " + str(gap))
        print("Update DB if the numbers look alright")
        update_db_input = input(
            "Do you want to update the DB? Press y or Y to confirm."
        )
        if update_db_input.upper() == "Y":
            query = (
                "INSERT INTO commledger (mtm_date,sharekhanbalance, mastertrustbalance,daypnl, mtmtoday, sharekhan_adjust, mastertrust_adjust,reserved1,reserved4,mtm_gap) VALUES ('"
                + date.strftime("%Y-%m-%d")
                + "'"
            )
            values = [
                sk_balance,
                gc_balance,
                pnl_today,
                running_mtm,
                kc_adjust,
                ks_adjust,
                inout_flow,
                reserved4,
                gap,
            ]
            for v in values:
                query = query + "," + str(v)
            query = query + ")"
            obj.portfolio_stats_engine.execute(query)
            display("MTM Database has been Updated")
        clear_dead_sheet_input = input(
            "Do you want to clear the dead sheet from logit object?"
        )
        if clear_dead_sheet_input.upper() == "Y":
            obj.dead = {}
            update_counts(obj=obj)
            update_df(obj=obj)
    except Exception as e:
        display(e)


def day_start_gift(obj: Logit):
    """Function to initialise gift morning variables.

    Args:
        obj (Logit): logit object
    """
    try:
        df_mtm = pd.read_sql(
            f"select * from {config.GIFT_LEDGER} ORDER BY mtm_date ASC",
            con=obj.portfolio_stats_engine,
        )
        date = obj.logit_date.strftime("%d%m%y")
        url = (
            f"https://www.nseix.com/api/content/daily_report/G_T_Bhavcopy_FO_{date}.CSV"
        )
        download_file(url=url, file_name="bhav_gift.csv")
        bhavcopy = pd.read_csv(
            "./csv/bhav_gift.csv",
            header=None,
            usecols=[0, 6],
            names=["contract_name", "close"],
            skiprows=1,
        )
        bhavcopy["segment"] = bhavcopy["contract_name"].str[0:6]
        bhavcopy = bhavcopy[bhavcopy["segment"].isin(["FUTSTK", "FUTIDX"])]
        bhavcopy["expiry"] = bhavcopy["contract_name"].str[-11:]
        bhavcopy.expiry = pd.to_datetime(bhavcopy.expiry)
        bhavcopy["symbol"] = bhavcopy["contract_name"].str[6:-11]

        bhavcopy.expiry = bhavcopy.expiry.apply(lambda x: x.strftime("%d-%b-%Y"))

        [running_mtm, inout_flow] = get_running_mtm_gift(
            obj.live, bhavcopy, obj.store_lot_size
        )
        booked_pnl_yesterday = df_mtm["daypnl"].sum()
        date = input(
            "Date for mtm , use date of last trading day in YYYY-MM-DD format : "
        )
        date = parser.parse(date).date()
        gap_yesterday = 0
        if len(df_mtm) > 0:
            gap_yesterday = df_mtm["mtm_gap"].iloc[-1]
        pnl_today = get_today_pnl_gift(obj.dead, obj.store_lot_size)
        print("PNL value for today's deads : ", pnl_today)  # Show PnL for DEAD
        print("PL Balance in Results Sheet: " + str(booked_pnl_yesterday + pnl_today))
        print("Running MTM on current Positions : " + str(running_mtm))
        sk_balance = str2num(input("Gift Ledger Balance on Globe : "))
        kc_adjust = str2num(
            input("Credit(+)/Debit(-) from Gift Ledger ? leave empty if none: ")
        )
        gap = (
            kc_adjust
            + df_mtm["nseixledger_adjust"].sum()
            + df_mtm["reserved4"].sum()
            + df_mtm["monthly_charges"].sum()
            + pnl_today
            + booked_pnl_yesterday
            + running_mtm
            + inout_flow
            - sk_balance
        )
        print("Gap in MTM as reported by external agents and us: " + str(gap))
        print("Corresponding Gap yesterday : " + str(gap_yesterday))
        reserved4 = str2num(
            input("Credit/Debit(-) gap adjustment ? leave empty if none: ")
        )
        if reserved4 != 0:
            gap = gap + reserved4
            print("New Gap in MTM as reported by external agents and us: " + str(gap))
        monthly_charges = str2num(input("Monthly charges ? leave empty if none: "))
        if monthly_charges != 0:
            gap = gap + monthly_charges
            print("New Gap in MTM as reported by external agents and us: " + str(gap))
        print("Update DB if the numbers look alright")
        update_db_input = input(
            "Do you want to update the DB? Press y or Y to confirm."
        )
        if update_db_input.upper() == "Y":
            query = (
                "INSERT INTO giftledger (mtm_date, nseixledgerbalance, daypnl, mtmtoday, nseixledger_adjust, cash_inflow_outflow, monthly_charges, reserved4, mtm_gap) VALUES ('"
                + date.strftime("%Y-%m-%d")
                + "'"
            )
            values = [
                sk_balance,
                pnl_today,
                running_mtm,
                kc_adjust,
                inout_flow,
                monthly_charges,
                reserved4,
                gap,
            ]
            for v in values:
                query = query + "," + str(v)
            query = query + ")"
            obj.portfolio_stats_engine.execute(query)
            display("MTM Database has been Updated")
        clear_dead_sheet_input = input(
            "Do you want to clear the dead sheet from logit object?"
        )
        if clear_dead_sheet_input.upper() == "Y":
            obj.dead = {}
            update_counts(obj=obj)
            update_df(obj=obj)
    except Exception as e:
        display(e)


def after_market_us(obj: Logit):
    """Function to initialise us variables, to be run after market on same day of trading.

    Args:
        obj (Logit): logit object
    """
    try:
        df = pd.read_csv(
            BytesIO(
                config.minioClient.get_object(
                    config.MINIO_US_DAILY_DATA_FILE_BUCKET_NAME,
                    config.MINIO_LIVE_DATA_LOCATION["futidx_fut_1_min_us"],
                ).data
            ),
            header=None,
            names=[
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "Next_Close",
                "Next_Cons_Volume",
            ],
        )
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        df.sort_values(by="timestamp", inplace=True)
        bhav_us = df[
            df.timestamp
            == pd.Timestamp(obj.logit_date) + pd.Timedelta(hours=22, minutes=0)
        ]

        def convert_to_contract(row):
            return config.balte_id_to_symbol[int(row["ID"])]

        bhav_us["contract"] = bhav_us.apply(convert_to_contract, axis=1)
        for c in bhav_us.contract:
            settle_price = float(input(f"Enter settlement price for {c}"))
            bhav_us.loc[bhav_us.contract == c, "Close"] = settle_price
        [running_mtm, cashinout_flow] = get_running_mtm_us(obj.live, bhav_us)
        pnl_today = get_today_pnl_us(obj.dead)
        commissions = 0
        if len(obj.df_dead):
            commissions = obj.df_dead.brokerage.sum() + obj.df_dead.others.sum()
        date = obj.logit_date.strftime("%Y-%m-%d")
        date = parser.parse(date).date()
        print("Running MTM (in USD):", running_mtm)
        print("Cashinout Flow (in USD):", cashinout_flow)
        print("Today's PnL (in USD):", pnl_today)
        df = pd.read_sql(
            "SELECT * FROM us_auxiliary_ledger",
            con=obj.portfolio_stats_engine,
        )
        if date.strftime("%Y-%m-%d") in df.mtm_date.to_list():
            print(f"Auxillary Ledger entry already present for {date}")
            delete_ledger_entry_input = str(
                input(
                    f"Do you want to delete auxillary ledger entry for {date}? Press y to confirm"
                )
            )
            if delete_ledger_entry_input.upper() == "Y":
                data_dict = {}
                data_dict["mtm_date"] = date
                sql_query = text(
                    """DELETE FROM us_auxiliary_ledger WHERE mtm_date = :mtm_date"""
                )
                with obj.portfolio_stats_engine.connect() as connection:
                    connection.execute(sql_query, **data_dict)
            else:
                raise ValueError(
                    "Cannot update auxillary ledger as entry is already present for the given date!"
                )
        update_db_input = input(
            "Do you want to update the DB? Press y or Y to confirm."
        )
        if update_db_input.upper() == "Y":
            query = (
                "INSERT INTO us_auxiliary_ledger (mtm_date, cashinflow, mtmtoday, todaypnl, commissions) VALUES ('"
                + date.strftime("%Y-%m-%d")
                + "'"
            )
            values = [
                cashinout_flow,
                running_mtm,
                pnl_today,
                commissions,
            ]
            for v in values:
                query = query + "," + str(v)
            query = query + ")"
            obj.portfolio_stats_engine.execute(query)
            display("US Auxiliary Database has been Updated")
    except Exception as e:
        display(e)


def get_ledger_df(mode: str, date: str):
    ledger_df: pd.DataFrame = fetch_df_from_minio(
        bucket_name=f"{mode.lower()}-production",
        filename=f"reports/{mode}_{date}.csv",
        parquet_mode=False,
        international_exchanges=True,
    )
    ledger_df.to_csv(f"./csv/{mode}_{date}.csv")
    if mode == "KRX":
        try:
            currency_df: pd.DataFrame = fetch_df_from_minio(
                bucket_name="krx-production",
                filename=f"reports/{mode}_CURRENCY_{date}.csv",
                parquet_mode=False,
                international_exchanges=True,
            )
            currency_df.to_csv(f"./csv/{mode}_CURRENCY_{date}.csv")
        except Exception as e:
            print(f"Could not download currency report due to {e}")

    df = pd.read_csv(f"./csv/{mode}_{date}.csv")
    unique_accounts = df["accountId"].unique()
    unique_accounts = [x for x in unique_accounts if x != "accountId"]
    unique_accounts = [
        x for x in unique_accounts if x in config.client_trading_account[mode]
    ]
    ending_ledger_balance = 0
    commissions = 0
    other_charges = 0
    for account in unique_accounts:
        account_df = df[df["accountId"] == account].reset_index(drop=True)
        ending_ledger_balance += float(
            account_df[account_df.symbol.isna()].iloc[-1]["balance"]
        )
        account_df.tradeCommission = account_df.tradeCommission.astype("float")
        commissions += account_df.tradeCommission.sum()
        account_df.amount = account_df.amount.astype("float")
        other_charges += account_df[account_df.symbol.isna()].amount.sum()
    return ending_ledger_balance, abs(commissions), other_charges


# Settlement for expired trades happens on the next day
def cashinflowoutflow_dead_sheet(df: pd.DataFrame, date: str) -> float:
    if len(df) == 0:
        return 0
    df = df[
        (df.expiry == pd.Timestamp(date))
        & (df.exit_timestamp == pd.Timestamp(date).replace(hour=22, minute=0))
    ]
    df["net_flow"] = df["total_quantity"] * df["exit_price"]
    return df.net_flow.sum()


def update_ledger_us(obj: Logit):
    date = input(
        "Date for mtm , use date of last trading day in YYYY-MM-DD format for which ledger is to be updated: "
    )
    df_auxiliary = pd.read_sql(
        f"select * from us_auxiliary_ledger WHERE mtm_date = '{date}'",
        con=obj.portfolio_stats_engine,
    )
    running_mtm = df_auxiliary["mtmtoday"].iloc[0]
    cashinout_flow = df_auxiliary["cashinflow"].iloc[0]
    pnl_today = df_auxiliary["todaypnl"].iloc[0]
    print(f"Running MTM for futures on {date} (in USD): ", running_mtm)
    print(f"Cash Inflow for options on {date} (in USD): ", cashinout_flow)
    print(f"PNL value for deads on {date} (in USD): ", pnl_today)
    df_mtm = pd.read_sql(
        "SELECT * FROM usledger ORDER BY mtm_date ASC", con=obj.portfolio_stats_engine
    )
    if df_mtm["mtm_date"].iloc[-1] == pd.Timestamp(date):
        print(f"Ledger entry already present for {date}")
        delete_ledger_entry_input = str(
            input(f"Do you want to delete ledger entry for {date}? Press y to confirm")
        )
        if delete_ledger_entry_input.upper() == "Y":
            data_dict = {}
            data_dict["mtm_date"] = date
            sql_query = text("""DELETE FROM usledger WHERE mtm_date = :mtm_date""")
            with obj.portfolio_stats_engine.connect() as connection:
                connection.execute(sql_query, **data_dict)
        else:
            raise ValueError(
                "Cannot update ledger as entry is already present for the given date!"
            )
    booked_pnl_previous = df_mtm["daypnl"].sum()
    gap_previous = 0
    if len(df_mtm) > 0:
        gap_previous = df_mtm["mtm_gap"].iloc[-1]
    print("PL Balance in Results Sheet: " + str(booked_pnl_previous + pnl_today))
    print("Running MTM on current Positions : " + str(running_mtm))
    holiday_list = pd.to_datetime(
        pd.read_csv(
            BytesIO(
                config.minioClient.get_object(
                    "commondata", "holiday_lists/us_spot_holiday_list.csv"
                ).data
            ),
            header=None,
        )[0]
    ).to_list()
    file_date: str = previous_date(
        np.sort(np.concatenate([config.ALL_DATES_US, holiday_list])),
        pd.Timestamp(date),
        lookback=-1,
    ).strftime("%Y-%m-%d")
    us_ledger_balance, commission, miscellaneous_costs = get_ledger_df(
        mode="US",
        date=file_date,
    )
    if us_ledger_balance == 0:
        print("Unable to load ledger balance from file. Enter ledger balance manually!")
        us_ledger_balance = str2num(input("USLedger Balance (in USD) : "))
    print(f"USLedger value: {us_ledger_balance}")
    print(f"Miscellaneous Costs recieved (in USD): {miscellaneous_costs}")
    print(f"Commissions reported (in USD): {commission}")
    commission_dead_sheet = df_auxiliary["commissions"].iloc[0]
    commission_gap = commission - commission_dead_sheet
    df_dead = pd.read_sql(
        f"SELECT * FROM dead_sheet_us where exit_date = '{date}'",
        con=obj.portfolio_stats_engine,
    )
    today_expired_trade_net_flow = cashinflowoutflow_dead_sheet(df=df_dead, date=date)
    print(f"Today's expired trades net flow: {today_expired_trade_net_flow}")

    us_adjust = str2num(
        input(
            "Credit(+)/Debit(-) from USLedger (US_Adjust in USD) ? leave empty if none: "
        )
    )
    gap = (
        df_mtm["gap_adjust"].sum()
        + df_mtm["us_adjust"].sum()
        + us_adjust
        + pnl_today
        + booked_pnl_previous
        + running_mtm
        - today_expired_trade_net_flow
        + cashinout_flow
        + miscellaneous_costs
        + df_mtm["miscellaneous_costs"].sum()
        - df_mtm["commission_gap"].sum()
        - commission_gap
        - us_ledger_balance
    )
    print("Gap in MTM (in USD) as reported by external agents and us: " + str(gap))
    print("Corresponding previous gap (in USD) : " + str(gap_previous))
    gap_adjust = str2num(
        input("Credit/Debit(-) gap adjustment (in USD) ? leave empty if none: ")
    )
    reserved1 = str2num(
        input("Additional gap adjustment (in USD) ? leave empty if none: ")
    )
    reserved4 = str2num(
        input("Any other secondary gap adjustments (in USD) ? leave empty if none: ")
    )
    if reserved1 or reserved4 or gap_adjust:
        gap = gap + reserved1 + reserved4 + gap_adjust
        print(
            "New Gap in MTM (in USD) as reported by external agents and us: " + str(gap)
        )
    print("Update DB if the numbers look alright")
    update_db_input = input("Do you want to update the DB? Press y or Y to confirm.")
    if update_db_input.upper() == "Y":
        query = """
        INSERT INTO usledger (
            "mtm_date",
            "us_ledger_balance",
            "miscellaneous_costs",
            "commissions",
            "cashinflow",
            "daypnl",
            "mtmtoday",
            "us_adjust",
            "reserved1",
            "reserved4",
            "gap_adjust",
            "mtm_gap",
            "commission_gap"
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        values = (
            str(date),
            us_ledger_balance,
            miscellaneous_costs,
            commission,
            cashinout_flow,
            pnl_today,
            running_mtm,
            us_adjust,
            reserved1,
            reserved4,
            gap_adjust,
            gap,
            commission_gap,
        )
        obj.portfolio_stats_engine.execute(query, values)
        try:
            os.remove(f"./csv/US_{file_date}.csv")
            print("Ledger file has been deleted from csv folder")
        except Exception as e:
            print(f"Error removing ledger file {repr(e)}")
        display(f"MTM Database has been Updated for {date}")


def update_ledger_krx(obj: Logit):
    date = input(
        "Date for mtm , use date of last trading day in YYYY-MM-DD format for which ledger is to be updated: "
    )
    df_auxiliary = pd.read_sql(
        f"select * from krx_auxiliary_ledger WHERE mtm_date = '{date}'",
        con=obj.portfolio_stats_engine,
    )
    file_date: str = previous_date(
        config.ALL_DATES_KRX,
        pd.Timestamp(date),
        lookback=-1,
    ).strftime("%Y-%m-%d")
    krx_ledger_balance, commission, miscellaneous_costs = get_ledger_df(
        mode="KRX",
        date=file_date,
    )
    try:
        usdkrw_df = pd.read_csv(f"./csv/KRX_CURRENCY_{file_date}.csv")
        usdkrw = (
            1 / usdkrw_df[usdkrw_df.fxRateToBase.isna() == False].fxRateToBase.iloc[0]
        )
    except Exception:
        print("Unable to load usd to krw file, please enter conversion manually")
        usdkrw = str2num(input("Enter one USD value in KRW for that day: "))
    print(f"USD to KRW Conversion : {usdkrw}")
    running_mtm = round(df_auxiliary["mtmtoday"].iloc[0] / usdkrw, 5)
    cashinout_flow = round(df_auxiliary["cashinflow"].iloc[0] / usdkrw, 5)
    pnl_today = round(df_auxiliary["todaypnl"].iloc[0] / usdkrw, 5)
    print(f"Running MTM for futures on {date} (in USD): ", running_mtm)
    print(f"Cash Inflow for options on {date} (in USD): ", cashinout_flow)
    print(f"PNL value for deads on {date} (in USD): ", pnl_today)
    df_mtm = pd.read_sql(
        "SELECT * FROM krxledger ORDER BY mtm_date ASC", con=obj.portfolio_stats_engine
    )
    if df_mtm["mtm_date"].iloc[-1] == pd.Timestamp(date):
        print(f"Ledger entry already present for {date}")
        delete_ledger_entry_input = str(
            input(f"Do you want to delete ledger entry for {date}? Press y to confirm")
        )
        if delete_ledger_entry_input.upper() == "Y":
            data_dict = {}
            data_dict["mtm_date"] = date
            sql_query = text("""DELETE FROM krxledger WHERE mtm_date = :mtm_date""")
            with obj.portfolio_stats_engine.connect() as connection:
                connection.execute(sql_query, **data_dict)
        else:
            raise ValueError(
                "Cannot update ledger as entry is already present for the given date!"
            )
    booked_pnl_previous = df_mtm["daypnl"].sum()
    gap_previous = 0
    if len(df_mtm) > 0:
        gap_previous = df_mtm["mtm_gap"].iloc[-1]
    print("PL Balance in Results Sheet: " + str(booked_pnl_previous + pnl_today))
    if krx_ledger_balance == 0:
        print("Unable to load ledger balance from file. Enter ledger balance manually!")
        krx_ledger_balance = str2num(input("KRXLedger Balance (in USD) : "))
    print(f"KRXLedger value: {krx_ledger_balance}")
    print(f"Miscellaneous Costs recieved (in USD): {miscellaneous_costs}")
    print(f"Commissions reported (in USD): {commission}")
    commission_dead_sheet = round(df_auxiliary["commissions"].iloc[0] / usdkrw, 5)
    commission_gap = commission - commission_dead_sheet
    us_adjust = str2num(
        input(
            "Credit(+)/Debit(-) from KRXLedger (KRX_Adjust in USD) ? leave empty if none: "
        )
    )
    gap = (
        df_mtm["gap_adjust"].sum()
        + df_mtm["krx_adjust"].sum()
        + us_adjust
        + pnl_today
        + booked_pnl_previous
        + running_mtm
        + cashinout_flow
        + miscellaneous_costs
        + df_mtm["miscellaneous_costs"].sum()
        - df_mtm["commission_gap"].sum()
        - commission_gap
        - krx_ledger_balance
    )
    print("Gap in MTM (in USD) as reported by external agents and us: " + str(gap))
    print("Corresponding previous gap (in USD) : " + str(gap_previous))
    gap_adjust = str2num(
        input("Credit/Debit(-) gap adjustment (in USD) ? leave empty if none: ")
    )
    reserved1 = str2num(
        input("Additional gap adjustment (in USD) ? leave empty if none: ")
    )
    reserved4 = str2num(
        input("Any other secondary gap adjustments (in USD) ? leave empty if none: ")
    )
    if reserved1 or reserved4 or gap_adjust:
        gap = gap + reserved1 + reserved4 + gap_adjust
        print(
            "New Gap in MTM (in USD) as reported by external agents and us: " + str(gap)
        )
    print("Update DB if the numbers look alright")
    update_db_input = input("Do you want to update the DB? Press y or Y to confirm.")
    if update_db_input.upper() == "Y":
        query = """
        INSERT INTO krxledger (
            "mtm_date",
            "krx_ledger_balance",
            "miscellaneous_costs",
            "commissions",
            "cashinflow",
            "daypnl",
            "mtmtoday",
            "krx_adjust",
            "reserved1",
            "reserved4",
            "gap_adjust",
            "mtm_gap",
            "usdkrw",
            "commission_gap"
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        values = (
            str(date),
            krx_ledger_balance,
            miscellaneous_costs,
            commission,
            cashinout_flow,
            pnl_today,
            running_mtm,
            us_adjust,
            reserved1,
            reserved4,
            gap_adjust,
            gap,
            usdkrw,
            commission_gap,
        )
        obj.portfolio_stats_engine.execute(query, values)
        try:
            os.remove(f"./csv/KRX_{file_date}.csv")
            os.remove(f"./csv/KRX_CURRENCY_{file_date}.csv")
            print("Ledger & Currency file has been deleted from csv folder")
        except Exception as e:
            print(f"Error removing ledger file {repr(e)}")
        display(f"MTM Database has been Updated for {date}")


def after_market_krx(obj: Logit):
    """Function to initialise krx variables, to be run after market on same day of trading.

    Args:
        obj (Logit): logit object
    """
    try:
        df = pd.read_csv(
            BytesIO(
                config.minioClient.get_object(
                    config.MINIO_KRX_DAILY_DATA_FILE_BUCKET_NAME,
                    config.MINIO_LIVE_DATA_LOCATION["futidx_fut_1_min_krx"],
                ).data
            ),
            header=None,
            names=[
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "Next_Close",
                "Next_Cons_Volume",
            ],
        )
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        bhav_krx = df[
            (
                df["timestamp"]
                == pd.Timestamp.now().normalize() + pd.Timedelta(hours=15, minutes=20)
            )
        ]
        [running_mtm, cashinout_flow] = get_running_mtm_krx(obj.live, bhav_krx)
        pnl_today = get_today_pnl_krx(obj.dead)
        date = obj.logit_date.strftime("%Y-%m-%d")
        date = parser.parse(date).date()
        print("Running MTM (in KRW):", running_mtm)
        print("Cashinout Flow (in KRW):", cashinout_flow)
        print("Today's PnL (in KRW):", pnl_today)
        commissions = 0
        if len(obj.df_dead):
            commissions = obj.df_dead.brokerage.sum() + obj.df_dead.others.sum()
        df = pd.read_sql(
            "SELECT * FROM krx_auxiliary_ledger",
            con=obj.portfolio_stats_engine,
        )
        if date.strftime("%Y-%m-%d") in df.mtm_date.to_list():
            print(f"Auxillary Ledger entry already present for {date}")
            delete_ledger_entry_input = str(
                input(
                    f"Do you want to delete auxillary ledger entry for {date}? Press y to confirm"
                )
            )
            if delete_ledger_entry_input.upper() == "Y":
                data_dict = {}
                data_dict["mtm_date"] = date.strftime("%Y-%m-%d")
                sql_query = text(
                    """DELETE FROM krx_auxiliary_ledger WHERE mtm_date = :mtm_date"""
                )
                with obj.portfolio_stats_engine.connect() as connection:
                    connection.execute(sql_query, **data_dict)
            else:
                raise ValueError(
                    "Cannot update auxillary ledger as entry is already present for the given date!"
                )
        update_db_input = input(
            "Do you want to update the DB? Press y or Y to confirm."
        )
        if update_db_input.upper() == "Y":
            query = (
                "INSERT INTO krx_auxiliary_ledger (mtm_date, cashinflow, mtmtoday, todaypnl, commissions) VALUES ('"
                + date.strftime("%Y-%m-%d")
                + "'"
            )
            values = [cashinout_flow, running_mtm, pnl_today, commissions]
            for v in values:
                query = query + "," + str(v)
            query = query + ")"
            obj.portfolio_stats_engine.execute(query)
            display("KRX Auxiliary Database has been Updated")
        clear_dead_sheet_input = input(
            "Do you want to clear the dead sheet from logit object?"
        )
        if clear_dead_sheet_input.upper() == "Y":
            obj.dead = {}
            update_counts(obj=obj)
            update_df(obj=obj)
    except Exception as e:
        display(e)


def live_sheet_processing(_event: widgets, obj: Logit, out: widgets):
    """Function to process live df to make the live sheet.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            live_sheet(obj=obj)
        except Exception as e:
            display(e)


def dead_sheet_processing(_event: widgets, obj: Logit, out: widgets):
    """Function to process dead df to make the dead sheet.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            dead_sheet(obj=obj)
        except Exception as e:
            display(e)


def enter_orders_manually_pending(
    _event: widgets,
    obj: Logit,
    id: int,
    segment: str,
    symbol: str,
    expiry: str,
    quantity: int,
    strategy: str,
    wgt_price: int,
    out: widgets,
):
    """Function to enter orders manually.

    Args:
        _event (widgets): trigger widget (Button)
        obj (Logit): logit object
        id (int): logit id
        segment (str): segment
        symbol (str): symbol
        expiry (str): expiry date
        quantity (int): quantity
        strategy (str): strategy name
        wgt_price (int): weighted price
        out (widgets): output widget
    """
    with out:
        out.clear_output()
        try:
            obj.df_pending.loc[id] = [
                dt.datetime.today(),
                segment,
                symbol,
                expiry,
                quantity,
                strategy,
                wgt_price,
                "XX",
                0,
                0,
                wgt_price,
            ]
            update_counts(obj=obj)
            update_df(obj=obj)
        except Exception as e:
            display(e)


def ncdex_pricing(
    _event: widgets,
    obj: Logit,
    out: widgets,
):
    """Function to update ncdex pricing.

    Args:
        obj (Logit): logit object
    """
    with out:
        try:
            pd.options.mode.chained_assignment = None
            update_df(obj=obj)
            update_counts(obj=obj)
            pending_orders = obj.df_pending.copy()
            pending_orders.timestamp = pd.to_datetime(
                pending_orders.timestamp, format="%Y-%m-%d %H:%M:%S.%f"
            )
            pending_orders.expiry = pd.to_datetime(
                pending_orders.expiry, format="%d-%b-%Y"
            ).dt.strftime("%d%b%Y")
            pending_orders["contract"] = (
                pending_orders.symbol + pending_orders.expiry
            ).str.upper()
            ncdex_orders = pd.read_csv(config.PATH_TO_NCDEX_TRADES)
            ncdex_orders["Trade Time"] = (
                ncdex_orders["Trade Date"] + " " + ncdex_orders["Trade Time"]
            )
            ncdex_orders["Trade Date"] = pd.to_datetime(
                ncdex_orders["Trade Date"], format="%d-%b-%Y"
            )
            ncdex_orders["Trade Time"] = pd.to_datetime(
                ncdex_orders["Trade Time"], format="%d-%b-%Y %H:%M:%S"
            )

            def place_prices(executed, pending):
                pending.sort_values("timestamp", inplace=True)
                executed.sort_values("Time", inplace=True)
                curr_pend = 0
                curr_exec = 0
                if pending.empty:
                    return
                while curr_exec < len(executed):
                    if (
                        pending.total_quantity.iloc[curr_pend]
                        - pending.executed_quantity.iloc[curr_pend]
                        >= executed.Qty.iloc[curr_exec]
                    ):
                        pending.weighted_price.iloc[curr_pend] = (
                            pending.weighted_price.iloc[curr_pend]
                            * pending.executed_quantity.iloc[curr_pend]
                            + executed.Qty.iloc[curr_exec]
                            * executed.Price.iloc[curr_exec]
                        ) / (
                            pending.executed_quantity.iloc[curr_pend]
                            + executed.Qty.iloc[curr_exec]
                        )
                        pending.executed_quantity.iloc[curr_pend] += executed.Qty.iloc[
                            curr_exec
                        ]
                        curr_exec += 1
                    else:
                        remaining_qty = (
                            pending.total_quantity.iloc[curr_pend]
                            - pending.executed_quantity.iloc[curr_pend]
                        )
                        pending.weighted_price.iloc[curr_pend] = (
                            pending.weighted_price.iloc[curr_pend]
                            * pending.executed_quantity.iloc[curr_pend]
                            + remaining_qty * executed.Price.iloc[curr_exec]
                        ) / pending.total_quantity.iloc[curr_pend]
                        pending.executed_quantity.iloc[
                            curr_pend
                        ] = pending.total_quantity.iloc[curr_pend]
                        executed.Qty.iloc[curr_exec] = (
                            executed.Qty.iloc[curr_exec] - remaining_qty
                        )
                        curr_pend += 1

            for i in set(pending_orders.contract):
                print(i)
                contract_pending = pending_orders[pending_orders.contract == i].copy()
                start_time = min(contract_pending.timestamp)
                contract_ncdex = ncdex_orders[
                    (ncdex_orders["Trading Symbol"] == i)
                    & (ncdex_orders["Trade Time"] >= start_time)
                ]
                if not (contract_ncdex.empty):
                    exec_buy = contract_ncdex[
                        contract_ncdex["Buy/Sell"] == "BUY"
                    ].copy()
                    exec_sell = contract_ncdex[
                        contract_ncdex["Buy/Sell"] == "SELL"
                    ].copy()
                    exec_buy["Trade Qty"] = exec_buy["Trade Qty"] / (
                        obj.store_lot_size[contract_pending.symbol.iloc[0]] * 0.1
                    )
                    exec_sell["Trade Qty"] = exec_sell["Trade Qty"] / (
                        obj.store_lot_size[contract_pending.symbol.iloc[0]] * 0.1
                    )
                    exec_buy = exec_buy[["Trade Price", "Trade Qty", "Trade Time"]]
                    exec_sell = exec_sell[["Trade Price", "Trade Qty", "Trade Time"]]
                    exec_buy.columns = ["Price", "Qty", "Time"]
                    exec_sell.columns = ["Price", "Qty", "Time"]
                    execqty_buy = exec_buy["Qty"].sum()
                    execqty_sell = exec_sell["Qty"].sum()
                else:
                    execqty_buy = 0
                    execqty_sell = 0

                pending_buy = contract_pending[
                    contract_pending.total_quantity > 0
                ].copy()
                pending_sell = contract_pending[
                    contract_pending.total_quantity < 0
                ].copy()
                pending_sell.total_quantity = pending_sell.total_quantity * -1
                pendqty_buy = pending_buy.total_quantity.sum()
                pendqty_sell = pending_sell.total_quantity.sum()

                if pendqty_buy == execqty_buy and pendqty_sell == execqty_sell:
                    place_prices(exec_buy, pending_buy)
                    place_prices(exec_sell, pending_sell)
                elif (
                    pendqty_buy - pendqty_sell == execqty_buy - execqty_sell
                    and (execqty_buy + execqty_sell) > 0
                    and pendqty_buy > 0
                    and pendqty_sell > 0
                ):
                    if execqty_buy > execqty_sell:
                        new_trade_time = min(pending_sell.timestamp)
                        new_trade_qty = pendqty_buy - execqty_buy
                        new_trade_price = (
                            exec_buy.Price * exec_buy.Qty
                        ).sum() / exec_buy.Qty.sum()
                    else:
                        new_trade_time = min(pending_buy.timestamp)
                        new_trade_qty = pendqty_sell - execqty_sell
                        new_trade_price = (
                            exec_sell.Price * exec_sell.Qty
                        ).sum() / exec_sell.Qty.sum()

                    exec_buy = exec_buy.append(
                        {
                            "Price": new_trade_price,
                            "Qty": new_trade_qty,
                            "Time": new_trade_time,
                        },
                        ignore_index=True,
                    )
                    exec_sell = exec_sell.append(
                        {
                            "Price": new_trade_price,
                            "Qty": new_trade_qty,
                            "Time": new_trade_time,
                        },
                        ignore_index=True,
                    )
                    place_prices(exec_buy, pending_buy)
                    place_prices(exec_sell, pending_sell)
                else:
                    print(
                        "Problem! {}, PendingBuyQty: {}, PendingSellQty: {}, ExecutedBuyQty: {}, ExecutedSellQty : {}".format(
                            i, pendqty_buy, pendqty_sell, execqty_buy, execqty_sell
                        )
                    )

                pending_sell.total_quantity = pending_sell.total_quantity * -1
                pending_sell.executed_quantity = pending_sell.executed_quantity * -1

                for i in pending_buy.index:
                    obj.df_pending.loc[i, "weighted_price"] = pending_buy.loc[
                        i, "weighted_price"
                    ]
                    obj.df_pending.loc[i, "executed_quantity"] = pending_buy.loc[
                        i, "executed_quantity"
                    ]

                for i in pending_sell.index:
                    obj.df_pending.loc[i, "weighted_price"] = pending_sell.loc[
                        i, "weighted_price"
                    ]
                    obj.df_pending.loc[i, "executed_quantity"] = pending_sell.loc[
                        i, "executed_quantity"
                    ]
            pd.options.mode.chained_assignment = "warn"
            obj.pending = obj.df_pending.to_dict("index")
            obj.scan_pending_again()
            update_counts(obj=obj)
            update_df(obj=obj)
        except Exception as e:
            pd.options.mode.chained_assignment = "warn"
            display(e)


def add_order_entries_for_buffered_exits_in_pending(
    _event: widgets, obj: Logit, out: widgets
):
    """Function to add order entries corresponding to buffered exits in pending.

    Args:
        obj (Logit): logit object
    """
    with out:
        out.clear_output()
        try:
            buffered_exits = obj.buffered_exits.copy()
            pending_entries = {}
            pending_trade_ids = list(
                obj.df_pending.index.astype("str").str[5:].values.astype("uint64")
            )
            cnt = 0
            for trade_id, trade_data in buffered_exits.items():
                if trade_id in pending_trade_ids:
                    continue
                cnt += 1
                pending_trade_id = "10011" + str(trade_id)
                pending_trade_data = trade_data.copy()
                pending_trade_data["executed_quantity"] = 0
                pending_trade_data["weighted_price"] = 0.0
                pending_trade_data["total_quantity"] *= -1
                pending_entries[int(pending_trade_id)] = pending_trade_data
            df_pending_entries = pd.DataFrame.from_dict(pending_entries, orient="index")
            obj.df_pending = obj.df_pending.append(df_pending_entries)
            obj.pending = obj.df_pending.to_dict(orient="index")
            update_df(obj=obj)
            update_counts(obj=obj)
            print(f"{cnt} entries added to pending")
        except Exception as e:
            pd.options.mode.chained_assignment = "warn"
            display(e)
