from minio import Minio
from io import BytesIO
import numpy as np
import datetime
import pickle

from typing import Dict

BALTE_LIVE_TABLE_NSE = "live"
BALTE_LIVE_TABLE_MCX = "mcx_production"
BALTE_LIVE_TABLE_KRX = "krx_production"
BALTE_LIVE_TABLE_GIFT = "gift_production"
BALTE_LIVE_TABLE_US = "us_production"
BALTE_LIVE_TABLE_BSE = "bse_production"
BALTE_LIVE_TABLE_NCDEX = "ncdex_live"
STRAT_TABLE = "curr_info"
DEAD_TRADE_LIVE_NSE = "dead_trade_live"
DEAD_TRADE_LIVE_20_DAYS_NSE = "dead_trade_live_20days"
DEAD_TRADE_LIVE_20_DAYS_MCX = "dead_trade_mcx_production"
DEAD_TRADE_TOTAL_MCX = "dead_trade_mcx_production_total"
DEAD_TRADE_LIVE_20_DAYS_KRX = "dead_trade_krx_production"
DEAD_TRADE_TOTAL_KRX = "dead_trade_krx_production_total"
DEAD_TRADE_TOTAL_GIFT = "dead_trade_gift_production_total"
DEAD_TRADE_TOTAL_US = "dead_trade_us_production_total"
DEAD_TRADE_LIVE_20_DAYS_BSE = "dead_trade_bse_production"
DEAD_TRADE_LIVE_20_DAYS_NCDEX = "ncdex_dead_trade_live"
DEAD_TRADE_LIVE_20_DAYS_GIFT = "dead_trade_gift_production"
DEAD_TRADE_LIVE_20_DAYS_US = "dead_trade_us_production"
KEY = b"lryFDBCpw5-Pb0Sg_b8hy0GVjcB7hX7Q6c4g2fZLPNQ="
ENTRY_OR_EXIT = 4
START_OF_ACTUAL_TRADE_ID = 5
STUB = "192.168.0.198:8999"
STUB_XN = "192.168.0.198:13005"
STUB_MCX = "192.168.0.198:14005"
STUB_NCDEX = "192.168.0.198:14000"
STUB_GIFT = "192.168.0.198:16005"
STUB_BSE = "192.168.0.198:15005"
MINIO_END_POINT = "192.168.0.198:11009"
MINIO_ACCESS_KEY = "super"
MINIO_SECRET_KEY = "doopersecret"
PATH_TO_MCX_FILE = "./csv/mcx_netpositions.xlsx"
PATH_TO_NCDEX_FILE = "./csv/ncdex_netpositions.csv"
PATH_TO_NCDEX_TRADES = "./csv/ncdex_trades.csv"
ALL_DATES_LOCATION = "balte_uploads/ALL_DATES.npy"
ALL_DATES_FX_LOCATION = "balte_uploads/ALL_DATES_FX.npy"
ALL_DATES_MCX_LOCATION = "balte_uploads/ALL_DATES_MCX.npy"
ALL_DATES_NCDEX_LOCATION = "balte_uploads/ALL_DATES_NCDEX.npy"
ALL_DATES_KRX_LOCATION = "all_dates/ALL_DATES_KRX.npy"
ALL_DATES_GIFT_LOCATION = "balte_uploads/ALL_DATES_GIFT.npy"
BUCKET_NAME = "commondata"
KRX_BUCKET_NAME = "international"
MINIO_KRX_DAILY_DATA_FILE_BUCKET_NAME = "krx"
MINIO_US_DAILY_DATA_FILE_BUCKET_NAME = "usa"
MINIO_LIVE_DATA_LOCATION: Dict[str, str] = {
    "optidx_1_min_krx": "krx/1_min/optidx/data.csv",
    "optidx_5_min_krx": "krx/5_min/optidx/data.csv",
    "futidx_fut_1_min_krx": "krx/1_min/fut_raw/data.csv",
    "futidx_fut_5_min_krx": "krx/5_min/fut_raw/data.csv",
    "futidx_fut_1_min_us": "us/1_min/fut_raw/data.csv",
    "futidx_fut_5_min_us": "us/5_min/fut_raw/data.csv",
    "optidx_1_min_us": "us/1_min/optidx/data.csv",
}
minioClient = Minio(MINIO_END_POINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False)
ALL_DATES_NSE = np.load(
    BytesIO(minioClient.get_object(BUCKET_NAME, ALL_DATES_LOCATION).data),
    allow_pickle=True,
    encoding="bytes",
)
ALL_DATES_FX = np.load(
    BytesIO(minioClient.get_object(BUCKET_NAME, ALL_DATES_FX_LOCATION).data),
    allow_pickle=True,
    encoding="bytes",
)
ALL_DATES_MCX = np.load(
    BytesIO(minioClient.get_object(BUCKET_NAME, ALL_DATES_MCX_LOCATION).data),
    allow_pickle=True,
    encoding="bytes",
)
ALL_DATES_NCDEX = np.load(
    BytesIO(minioClient.get_object(BUCKET_NAME, ALL_DATES_NCDEX_LOCATION).data),
    allow_pickle=True,
    encoding="bytes",
)
ALL_DATES_KRX = np.load(
    BytesIO(minioClient.get_object(KRX_BUCKET_NAME, ALL_DATES_KRX_LOCATION).data),
    allow_pickle=True,
    encoding="bytes",
)
ALL_DATES_GIFT = np.load(
    BytesIO(minioClient.get_object(BUCKET_NAME, ALL_DATES_GIFT_LOCATION).data),
    allow_pickle=True,
    encoding="bytes",
)
ALL_DATES_US = pickle.loads(
    minioClient.get_object("international", "all_dates/ALL_DATES_INTERNATIONAL").data
)["us"]
KOSPI_FUT_EXPIRY_DICT = np.load(
    BytesIO(
        minioClient.get_object(
            "international", "expiry_dict/kospi_fut_expiry_dict"
        ).data
    ),
    allow_pickle=True,
)
US_FUT_EXPIRY_DICT = np.load(
    BytesIO(
        minioClient.get_object(
            "international", "expiry_dict/us_futures_expiry_dict"
        ).data
    ),
    allow_pickle=True,
)
LIVE_DEAD_SHEET_BUCKET = "balte"
CURRENCY_LEDGER = "curledger"
EQ_LEDGER = "eqledger"
COMM_LEDGER = "commledger"
US_LEDGER = "usledger"
GIFT_LEDGER = "giftledger"
KRX_AUXILIARY_LEDGER = "krx_auxiliary_ledger"
US_AUXILIARY_LEDGER = "us_auxiliary_ledger"
reports_bucket = "132_comp_files/"
FNO_MARKET_CLOSE = datetime.time(15, 30)
FUTCOM_INSTRUMENTS = [
    "CASTOR",
    "CHANA",
    "COCUDAKL",
    "DHANIYA",
    "GUARGUM5",
    "GUARSEED10",
    "JEERAUNJHA",
    "RMSEED",
    "SYBEANIDR",
    "SYOREF",
    "TMCFGRNZM",
    "SUGARM",
]
MONTHS = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
]
SEGMENTS = {
    "NSE": ["NF", "CASH", "BSE_NF"],
    "COMM": ["MCX"],
    "KRX": ["OPTIDX_KRX", "FUTIDX_KRX"],
    "GIFT": ["GIFT_NF"],
    "US": ["FUTIDX_US", "OPTIDX_US"],
}
FUTURES_UNIVERSE = [
    "FUTSTK",
    "FUTIDX",
    "FUTCUR",
    "FUTCOM",
    "FUTIDX_KRX",
    "FUTIDX_GIFT",
    "FUTIDX_BSE",
    "FUTSTK_BSE",
    "FUTIDX_US",
]
OPTIONS_UNIVERSE = [
    "OPTIDX",
    "OPTSTK",
    "OPTCOM",
    "OPTCUR",
    "OPTIDX_KRX",
    "OPTIDX_BSE",
    "OPTSTK_BSE",
    "OPTIDX_GIFT",
    "OPTIDX_US",
]
FUTURES_UNIVERSE_MAPPING = {
    "NSE": ["FUTSTK", "FUTIDX", "FUTCUR", "FUTIDX_BSE", "FUTSTK_BSE"],
    "COMM": ["FUTCOM"],
    "KRX": ["FUTIDX_KRX"],
    "GIFT": ["FUTIDX_GIFT"],
    "US": ["FUTIDX_US"],
}
OPTIONS_UNIVERSE_MAPPING = {
    "NSE": ["OPTIDX", "OPTSTK", "OPTIDX_BSE", "OPTCUR", "OPTSTK_BSE"],
    "COMM": ["OPTCOM"],
    "KRX": ["OPTIDX_KRX"],
    "GIFT": ["OPTIDX_GIFT"],
    "US": ["OPTIDX_US"],
}
SLEEP_TIME = 1
FAILED_ATTEMPTS_THRESHOLD = 2
START_TIME = {
    "NSE": "09:16",
    "COMM": "09:16",
    "KRX": "09:16",
    "GIFT": "03:31",
    "US": "00:01",
}
START_TIME_KRX = "09:01"
START_TIME_US = datetime.time(0, 1)
OPTIDX_SYMBOL_LIST = [
    "NIFTY",
    "BANKNIFTY",
    "FINNIFTY",
    "MIDCPNIFTY",
    "SENSEX",
    "BANKEX",
    "CRUDEOIL" "NATURALGAS",
]
TRANSACTION_CHARGES_US = {
    "US_COMPQ": (0.25, 0.35),
    "US_S&P": (0.25, 0.35),
    "US_RUT": (0.85, 1.38),
}

symbol_to_balte_id = pickle.loads(
    minioClient.get_object("commondata", "balte_uploads/mapping_dict").data
)

balte_id_to_symbol = {}
for key, value in symbol_to_balte_id.items():
    balte_id_to_symbol[value] = key

DATES_TO_INDEX = {}
counter: int = 0
for date in ALL_DATES_US:
    DATES_TO_INDEX[date] = counter
    counter += 1
client_trading_account = {
    "US": ["U19294600"],
    "KRX": ["U19294600F"],
}
MINIO_END_POINT_INTERNATIONAL = "192.168.0.209:9000"
MINIO_ACCESS_KEY_INTERNATIONAL = "minioadmin"
MINIO_SECRET_KEY_INTERNATIONAL = "minioadmin"
minioClientInternationalExchanges = Minio(
    MINIO_END_POINT_INTERNATIONAL,
    MINIO_ACCESS_KEY_INTERNATIONAL,
    MINIO_SECRET_KEY_INTERNATIONAL,
    secure=False,
)
EXCHANGE_WISE_VAULT_CONFIGURATIONS = {
    "NSE": {
        "role": "e35e79f5-204a-8b75-8bdf-d065288982d2",
        "secret": "********-8817-b556-b085-bc1cc9f15536",
        "path": "database/static-creds/postgres_nse",
    },
    "KRX": {
        "role": "c5247e80-e22a-2f88-fd2d-490da799b4cb",
        "secret": "151bbcb5-cd72-8202-2c20-4313c541f208",
        "path": "database/static-creds/postgres_krx",
    },
    "US": {
        "role": "b5f7bb56-02b6-053b-6431-e5048bb5d9cf",
        "secret": "b7343793-24b6-215d-368e-ba4b447eb6cb",
        "path": "database/static-creds/postgres_us",
    },
    "COMM": {
        "role": "ac2efa4e-db6c-6d04-62df-90bee43ebb8d",
        "secret": "d8d0d38b-c534-1573-2446-bfb0e4136fcc",
        "path": "database/static-creds/postgres_comm",
    },
    "GIFT": {
        "role": "ae67d164-0af4-ac7d-f2df-1e0208403d73",
        "secret": "df22fb3c-9877-96c8-0a64-b1fd818bd9f2",
        "path": "database/static-creds/postgres_gift",
    },
}
VAULT = {
    "url": "http://192.168.0.198:8300",
    "role": "9b89ad10-baab-63a9-415b-569091e11ded",
    "secret": "b894085b-eb19-b090-1dd3-302c280e1a35",
    "path": "credentials/data/access/samba_creds",
}
