from logit.utility import (
    trade_sanity_check,
    dump_to_csv,
    load_from_csv,
    load_from_csv_mcx,
    load_from_csv_krx,
    load_from_csv_us,
    load_from_csv_gift,
    calc_charges,
    convert_dead_to_live,
    generate_error_trade_string,
    compare_df,
    get_engine,
    previous_date,
    fetch_df_from_minio,
    apply_corporate_actions,
    get_todays_segment_list,
    minio_file_downloader,
)
import pytz
from logit.parse import parse_execution_logs, parse_new_order_logs
from logit.data_utility import *
import logging
import pandas as pd
import qgrid
import logit.config as config
from typing import List, Union
from IPython.display import HTML as display
import ipywidgets as widgets
from hvac import Client


class Logit:
    """Class having all attributes and methods related to logit.

    Attributes:
        live (dict): live order dict
        df_live (pd.DataFrame): live order df
        dead (dict): dead order dict
        df_dead (pd.DataFrame): dead order df
        pending (dict): pending order dict
        df_pending (pd.DataFrame): pending order df
        buffered_orders (dict): buffered order dict
        store_lot_size (dict): store lot dict
        store_sharekhan (dict): sharekhan dict
        data1min (dict): dict to store 1min data
        key (bytes): key used to encrypt files before dumping
        qgrid_widget (qgrid): qgrid widget to show selected df view
        live_count (widgets.HTML): widget to show live count
        dead_count (widgets.HTML): widget to show dead count
        pending_count (widgets.HTML): widget to show pending count
        last_processed (widgets.HTML): widget to show last processed symbol
        status (widgets.HTML): widget to show engine status
        mode (str): string to denote whether we are trading in commodities
        processed_messages (dict): dict to store the processed message by different topics
    """

    def __init__(
        self,
        log_file_name: str = None,
        lot_info: dict = {},
        mode: str = "NSE",
        **kwargs,
    ):
        """Initialise logit object.

        Args:
            log_file_name (str, optional): file path to log file. Defaults to None.
            lot_info (dict, optional): lot info dictionary. Defaults to {}.
            mode (str, optional): indicates if we are trading in commodities or not. Defaults to NSE.
        """
        self.timezone = kwargs.get("timezone", pytz.timezone("Asia/Kolkata"))
        self.store_lot_size = lot_info
        self.order_id_trade_id_mapping = {}
        self.partial_execution_df = {}
        self.no_execution_df = {}
        self.already_exitted = set()
        self.limit_order_cash_strategies = kwargs.get("limit_order_cash_strategies", [])
        if log_file_name is None:
            log_file_name = "log.log"
        logging.basicConfig(filename=log_file_name, filemode="w", level=logging.WARNING)

        client = Client(url=config.VAULT["url"])
        client.auth.approle.login(config.VAULT["role"], config.VAULT["secret"])
        result = client.read(config.VAULT["path"])["data"]["data"]["data"]
        stratURI = f"mysql+pymysql://{result['strat_db_username']}:{result['strat_db_password']}@{result['host']}:{result['strat_db_port']}/strat_db"
        self.strat_db_engine = get_engine(config_str=stratURI)
        centralised_oms_URI = f"mysql+pymysql://{result['balte_oms_centralized_username']}:{result['balte_oms_centralized_password']}@{result['balte_oms_centralized_ip']}:{result['strat_db_port']}/balte_oms"
        self.centralised_oms_engine = get_engine(config_str=centralised_oms_URI)
        oms_URI = f"mysql+pymysql://{result['balte_oms_199_username']}:{result['balte_oms_199_password']}@{result['balte_oms_199_ip']}:{result['strat_db_port']}/balte_oms"
        self.nse_oms_engine = get_engine(config_str=oms_URI)

        client_portfolio_stats = Client(url=config.VAULT["url"])
        exchange_wise_vault_config = config.EXCHANGE_WISE_VAULT_CONFIGURATIONS[mode]
        client_portfolio_stats.auth.approle.login(
            exchange_wise_vault_config["role"], exchange_wise_vault_config["secret"]
        )
        response = client_portfolio_stats.read(exchange_wise_vault_config["path"])[
            "data"
        ]
        portfolioURI = f"postgresql+psycopg2://{response['username']}:{response['password']}@{result['host']}:{result['port']}/portfolio_stats"
        self.portfolio_stats_engine = get_engine(config_str=portfolioURI)

        self.logit_date = input(
            "Enter the date for which logit is being run in (YYYY-MM-DD) format: "
        )
        try:
            self.logit_date = pd.Timestamp(self.logit_date).date()
        except Exception:
            raise Exception("Please enter the date in correct format")
        loading_ledger = input(
            "Do you want to load yesterday's dead sheet to update the ledger? Press y or Y to confirm."
        )
        ALL_DATES = (
            config.ALL_DATES_NSE
            if mode == "NSE"
            else (
                config.ALL_DATES_KRX
                if mode == "KRX"
                else config.ALL_DATES_MCX
                if mode == "COMM"
                else config.ALL_DATES_GIFT
                if mode == "GIFT"
                else config.ALL_DATES_US
            )
        )
        if mode in ["NSE", "GIFT", "KRX", "US"]:
            previous_working_day = previous_date(
                ALL_DATES=ALL_DATES, date=pd.Timestamp(str(self.logit_date)), lookback=1
            ).date()
        elif mode == "COMM":
            last_date = pd.read_sql(
                f"select mtm_date from {config.COMM_LEDGER} ORDER BY mtm_date DESC LIMIT 2",
                con=self.portfolio_stats_engine,
            )
            if loading_ledger.upper() == "Y":
                previous_working_day = previous_date(
                    ALL_DATES=ALL_DATES,
                    date=pd.Timestamp(str(last_date.mtm_date[0])),
                    lookback=-1,
                ).date()
            else:
                previous_working_day = previous_date(
                    ALL_DATES=ALL_DATES,
                    date=pd.Timestamp(str(last_date.mtm_date[1])),
                    lookback=-1,
                ).date()
        previous_working_day = dt.datetime.strptime(
            str(previous_working_day), "%Y-%m-%d"
        ).strftime("%Y%m%d")
        print("Loading previous working day's live sheet")
        try:
            self.df_live = fetch_df_from_minio(
                bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                filename=f"live_sheet_{mode}_{previous_working_day}.parquet",
            )
            self.df_live.rename(
                columns={
                    "entry_price": "weighted_price",
                    "entry_timestamp": "timestamp",
                },
                inplace=True,
            )
            print("Previous working day's live sheet loaded")
        except Exception as e:
            print(f"Previous day live sheet could not be loaded due to {repr(e)}")
            self.df_live = pd.DataFrame()
        self.live = self.df_live.to_dict(orient="index")
        self.dead = {}
        if mode in ["NSE", "GIFT", "KRX", "US"]:
            previous_working_day = previous_date(
                ALL_DATES=ALL_DATES, date=pd.Timestamp(str(self.logit_date)), lookback=1
            ).date()
        else:
            last_date = pd.read_sql(
                f"select mtm_date from {config.COMM_LEDGER} ORDER BY mtm_date DESC LIMIT 2",
                con=self.portfolio_stats_engine,
            )
            if loading_ledger.upper() == "Y":
                previous_working_day = previous_date(
                    ALL_DATES=ALL_DATES,
                    date=pd.Timestamp(str(last_date.mtm_date[0])),
                    lookback=-1,
                ).date()
            else:
                previous_working_day = previous_date(
                    ALL_DATES=ALL_DATES,
                    date=pd.Timestamp(str(last_date.mtm_date[1])),
                    lookback=-1,
                ).date()
        if loading_ledger.upper() == "Y":
            print("Loading previous working days's dead sheet")
            try:
                table_name: str = f"dead_sheet_{mode.lower()}"
                if mode == "GIFT":
                    gift_previous_working_day = pd.Timestamp(
                        previous_working_day
                    ).replace(hour=16, minute=30)
                    gift_current_session = pd.Timestamp(self.logit_date).replace(
                        hour=16, minute=0
                    )
                    dead_sheet = pd.read_sql(
                        f"select * from {table_name} where exit_timestamp between '{str(gift_previous_working_day)}' and '{str(gift_current_session)}'",
                        con=self.portfolio_stats_engine,
                    )
                else:
                    dead_sheet = pd.read_sql(
                        f"select * from {table_name} where exit_timestamp between '{str(previous_working_day)}' and '{str(self.logit_date)}'",
                        con=self.portfolio_stats_engine,
                    )
                if mode in ["COMM"]:
                    dead_sheet["total_quantity"] = dead_sheet.apply(
                        lambda row: row["total_quantity"]
                        / self.store_lot_size[row["symbol"]],
                        axis=1,
                    )
                dead_sheet = dead_sheet.drop(["id"], axis=1).set_index(["logit_id"])
                self.dead = dead_sheet.to_dict(orient="index")
            except Exception as e:
                print(e)
                df = fetch_df_from_minio(
                    bucket_name=config.LIVE_DEAD_SHEET_BUCKET,
                    filename=f"dead_sheet_{mode}.parquet",
                )
                df = df[
                    (df["exit_timestamp"].dt.date >= previous_working_day)
                    & (df["exit_timestamp"].dt.date < self.logit_date)
                ]
                self.dead = df.to_dict(orient="index")
            print("Previous working day's dead sheet loaded")
        self.df_dead = pd.DataFrame()
        self.pending = {}
        self.df_pending = pd.DataFrame()
        self.buffered_orders = {}
        self.store_sharekhan = {}
        self.data1min = {}
        self.key = config.KEY
        self.qgrid_widget = qgrid.show_grid(pd.DataFrame(), show_toolbar=True)
        self.live_count = widgets.HTML()
        self.dead_count = widgets.HTML()
        self.pending_count = widgets.HTML()
        self.last_processed = widgets.HTML()
        self.status = widgets.HTML()
        self.status.value = "Engine is IDLE"
        self.mode = mode
        self.processed_messages = {}
        self.ltp = pd.DataFrame()
        segment_list = get_todays_segment_list(
            pd.Timestamp.now().normalize(), self.mode
        )
        start_time = pd.Timestamp(config.START_TIME[mode])
        curr_time = pd.Timestamp.now() - pd.Timedelta(minutes=1)
        if self.mode == "KRX":
            start_time = pd.Timestamp(config.START_TIME_KRX)
            curr_time = pd.Timestamp.now(tz="Asia/Seoul").replace(
                tzinfo=None
            ) - pd.Timedelta(minutes=1)
        elif self.mode == "US":
            curr_time = pd.Timestamp.now(tz=self.timezone).replace(
                tzinfo=None
            ) - pd.Timedelta(minutes=1)
            start_time = curr_time - pd.Timedelta(minutes=1)
        print("Fetching last traded prices. Please wait.")
        while start_time < curr_time:
            for segment in segment_list:
                try:
                    _df = get_ltp(
                        segment=segment,
                        hour1=start_time.hour,
                        minute1=start_time.minute,
                        logit_date=self.logit_date,
                    )
                    if len(_df) == 0:
                        continue
                    self.ltp = pd.concat([self.ltp, _df])
                    self.ltp.drop_duplicates(
                        subset="contract", keep="last", inplace=True
                    )
                except Exception as e:
                    pass
            start_time += pd.Timedelta(minutes=1)
        self.buffered_entries = {}
        self.buffered_exits = {}
        self.corpact_applied = False
        try:
            minio_file_downloader(
                "commondata",
                "132_comp_files/changed_symbol.csv",
                "./csv/changed_symbol.csv",
            )
            df = pd.read_csv("./csv/changed_symbol.csv")
            if (
                len(df) == 0
                or len(
                    df[
                        pd.to_datetime(df.modification_date)
                        == pd.Timestamp(self.logit_date)
                    ]
                )
                == 0
            ):
                return
            df = df[
                pd.to_datetime(df.modification_date) == pd.Timestamp(self.logit_date)
            ]
            print(f"{len(df)} symbol changes!")
            print(df)
            self.df_live["symbol"] = (
                self.df_live["symbol"]
                .map(df.set_index("older_symbol")["new_symbol"])
                .fillna(self.df_live["symbol"])
            )
            self.live = self.df_live.to_dict(orient="index")
        except Exception as e:
            print(f"Could not change symbols due to {e}")

    def handle_message_proto(self, message: str) -> str:
        """Function to parse incoming message.

        Args:
            message (str): incoming message

        Returns:
            str: current symbol after processing
        """
        current_symbol = None
        if "system_status" in message:
            topic = message["topic"]
            self.processed_messages[topic] = []
        elif "send_order_log" in message:
            topic = message["topic"]
            if topic not in self.order_id_trade_id_mapping:
                self.order_id_trade_id_mapping[topic] = {}
            self.order_id_trade_id_mapping[topic][
                message["send_order_log"]["order_id"]
            ] = message["send_order_log"]["trade_id"]
            return None
        elif "cancellation_log" in message:
            topic = message["topic"]
            if topic not in self.processed_messages:
                self.processed_messages[topic] = []
            if message["message_id"] in self.processed_messages[topic]:
                return None
            if topic not in self.order_id_trade_id_mapping:
                self.order_id_trade_id_mapping[topic] = {}
            trade_id = self.order_id_trade_id_mapping[topic][
                message["cancellation_log"]["order_id"]
            ]
            if str(trade_id)[0] != "4":
                return None
            # Process only normal orders
            trade_id = int(trade_id)
            if trade_id in self.pending:
                if (
                    self.pending[trade_id]["strategy"]
                    not in self.limit_order_cash_strategies
                ):
                    return None
                if (
                    "executed_qty" in message["cancellation_log"]
                    and message["cancellation_log"]["executed_qty"] != 0
                ):
                    multiplier = (
                        -1 if self.pending[trade_id]["total_quantity"] < 0 else 1
                    )
                    self.partial_execution_df[trade_id] = self.pending[trade_id].copy()
                    self.pending[trade_id]["total_quantity"] = int(
                        multiplier * message["cancellation_log"]["executed_qty"]
                    )
                    self.scan_pending_again()
                else:
                    self.no_execution_df[trade_id] = self.pending[trade_id].copy()
                    del self.pending[trade_id]

        elif "new_order_log" in message:
            topic = message["topic"]
            if topic not in self.processed_messages:
                self.processed_messages[topic] = []
            if message["message_id"] in self.processed_messages[topic]:
                return None
            trade_id, trade_dict = parse_new_order_logs(
                message_dict=message, store_sharekhan=self.store_sharekhan
            )
            if (trade_dict["segment"] in ["OPTIDX", "OPTIDX_BSE"]) and (
                trade_dict["timestamp"].time() >= config.FNO_MARKET_CLOSE
            ):
                logging.warning("OPTIDX trade after market close")
                return None
            if (int(trade_id) in self.pending) and (trade_dict["segment"] == "CASH"):
                # Update exit timestamp according to the latest NOL.
                self.pending[int(trade_id)]["timestamp"] = trade_dict["timestamp"]
            current_symbol = trade_dict["symbol"]
            sanity_check = trade_sanity_check(self.pending, trade_id, trade_dict)
            if sanity_check:
                if trade_dict["total_quantity"] != 0:
                    tm = trade_dict["timestamp"]
                    tm = pd.Timestamp(
                        year=tm.year,
                        month=tm.month,
                        day=tm.day,
                        hour=tm.hour,
                        minute=tm.minute,
                    )
                    if trade_dict["segment"] in ["FUTIDX_US"]:
                        trade_dict["total_quantity"] *= self.store_lot_size[
                            current_symbol
                        ]
                    elif trade_dict["segment"] in ["OPTIDX_US"]:
                        trade_dict["total_quantity"] *= self.store_lot_size[
                            current_symbol + "_OPTIDX"
                        ]
                    elif trade_dict["segment"] in ["FUTIDX_KRX", "OPTIDX_KRX"]:
                        trade_dict["total_quantity"] *= 250000

                    self.pending[int(trade_id)] = trade_dict
                    self.processed_messages[topic].append(message["message_id"])
            else:
                logging.critical("AmbiguousMessage")
        elif "execution_log" in message:
            topic = message["topic"]
            if topic not in self.processed_messages:
                self.processed_messages[topic] = []
            if (message["message_id"] in self.processed_messages[topic]) or (
                "trade_qty" not in message["execution_log"]
            ):
                self.processed_messages[topic].append(message["message_id"])
                return None
            trade_id, order_dict = parse_execution_logs(message_dict=message)
            if int(trade_id) in self.pending:
                segment = self.pending[int(trade_id)]["segment"]
                if (segment in ["OPTIDX", "OPTIDX_BSE"]) and (
                    order_dict["timestamp"].time() >= config.FNO_MARKET_CLOSE
                ):
                    logging.warning("OPTIDX trade after market close")
                    return None
                if segment in ["OPTCUR", "FUTCUR"]:
                    order_dict["execution_price"] /= 100
                elif segment in ["FUTIDX_US"]:
                    order_dict["quantity"] *= self.store_lot_size[
                        self.pending[int(trade_id)]["symbol"]
                    ]
                elif segment in ["OPTIDX_US"]:
                    order_dict["quantity"] *= self.store_lot_size[
                        self.pending[int(trade_id)]["symbol"] + "_OPTIDX"
                    ]
                elif segment in ["FUTIDX_KRX", "OPTIDX_KRX"]:
                    order_dict["quantity"] *= 250000

            current_symbol = order_dict["symbol"]
            last_order_for_trade = self.process_order(trade_id, order_dict)
            if last_order_for_trade is not False:
                self.process_completed_trade(trade_id, last_order_for_trade)
            self.processed_messages[topic].append(message["message_id"])
        return current_symbol

    def scan_pending_again(self, lst_of_trade_ids: List = None):
        """Function to scan pending orders again.

        Args:
            lst_of_trade_ids (List, optional): list specifying only to look for specific tradeIDs. Defaults to None.

        Description:
            * Look for any completed trades in pending.

            * Call after manually updating pending with "EXIT PRICES" and "EXECUTED QUANTITY".

            * If only specific trade_ids are to be processed, pass a list of trade_ids.

            * Since entry always comes before exit and python3.7 and later dicts maintain insertion order,
            this func will build entry/exit pairs. If this order is disturbed then will fail to build exit.
        """
        if lst_of_trade_ids is not None:
            tmp = lst_of_trade_ids  # Only specific trade_ids
        else:
            tmp = list(self.pending.keys())  # All trade_ids

        # Re-implements some logic of process_order
        for trade_id in tmp:
            trade_dict = self.pending[int(trade_id)]
            trade_id = str(trade_id)
            if (
                trade_dict["executed_quantity"] == trade_dict["total_quantity"]
            ):  # Order has been completed
                if int(str(trade_id)[5:]) in self.dead:
                    print(
                        f"WARNING: This tradeid {trade_id} already exists in dead sheet. Assure that it is a rollover trade."
                    )
                result = self.check_before_deleting(trade_id, trade_dict)
                if result is not False:
                    del self.pending[int(trade_id)]
                    logging.info("ManuallyRemovedFromPending %s", trade_id)
                    self.process_completed_trade(trade_id, trade_dict)

    def _create_error_trade(self, trade_id: str) -> str:
        """Internal function to create error trade.

        Args:
            trade_id (str): tradeID for error trade

        Raises:
            ValueError: if tradeID is not valid

        Returns:
            str: error tradeID
        """

        trade_id = int(trade_id)
        trade_dict = self.pending[trade_id]
        passed_strategy_name = trade_dict["strategy"]
        remaining_quantity = (
            trade_dict["total_quantity"] - trade_dict["executed_quantity"]
        )
        price = trade_dict["weighted_price"]

        # Step1 : Get the trade out of pending
        self.pending[trade_id]["executed_quantity"] = self.pending[trade_id][
            "total_quantity"
        ]
        self.pending[trade_id]["weighted_price"] = price
        self.scan_pending_again([trade_id])

        # Step2 : Generate Error trade_id
        for i in range(610000, 999999):
            if i not in self.live:
                error_trade_id = i
                break

        # Step3 : Generate Error Trade
        effective_trade_id = int(str(trade_id)[config.START_OF_ACTUAL_TRADE_ID :])

        if (
            str(trade_id)[config.ENTRY_OR_EXIT] == "1"
        ):  # Trade was an entry, look in live
            try:
                trade_dict = self.live[effective_trade_id].copy()
            except (
                KeyError
            ):  # If exit was in buffer, trade immediately exits, so look in dead
                trade_dict = self.dead[effective_trade_id].copy()
                trade_dict = convert_dead_to_live(trade_dict)

        elif (
            str(trade_id)[config.ENTRY_OR_EXIT] == "0"
        ):  # Trade was en exit, look in dead
            trade_dict = self.dead[effective_trade_id].copy()
            trade_dict = convert_dead_to_live(trade_dict)

        else:
            raise ValueError("TradeID for making error trade is not valid")

        if (
            passed_strategy_name == trade_dict["strategy"]
        ):  # User didnt change strategy name
            trade_dict["strategy"] = "error"
        else:
            trade_dict["strategy"] = passed_strategy_name

        trade_dict["total_quantity"] = -1 * remaining_quantity
        self.live[int(error_trade_id)] = trade_dict
        logging.warning("ManuallyAddedErrorTrade %s %s" % (trade_id, error_trade_id))
        return error_trade_id

    def get_order_string_for_error(self, trade_ids: List[str]):
        """Function to generate order string for error tradeID.

        Args:
            trade_ids (List[str]): list of tradeIDs
        """
        for trade_id in trade_ids:
            trade_id = int(trade_id)
            tmp = generate_error_trade_string(trade_id, self.live[trade_id])
            print(tmp)

    def create_error_trade(self, trade_ids: List[str]):
        """Function to create error trades.

        Args:
            trade_ids (List[str]): list of tradeIDs

        Description:
            * For entries in pending, pass a list of trade_ids and the entries will be removed to live/dead and
            an error entry will be made in live from the remaining quantity.
        """
        for trade_id in trade_ids:
            tmp = self._create_error_trade(trade_id)
            print(
                "Removed %s & Created Error Trade with trade_di %s " % (trade_id, tmp)
            )

    def exit_error_manually(self, trade_id: str, exit_price: float):
        """Function to exit orders manually.

        Args:
            trade_id (str): tradeID
            exit_price (float): exit price

        Raises:
            ValueError: if no such trade exists
        """
        trade_id = int(trade_id)
        try:
            fake_trade_dict = self.live[trade_id].copy()
        except KeyError:
            raise ValueError("no such trade_id exists")

        # Artificially Create a pending like dictionary
        # display("REAL: ", self.live[trade_id])
        fake_trade_dict["weighted_price"] = exit_price
        fake_trade_dict["total_quantity"] = (
            -1 * fake_trade_dict["total_quantity"]
        )  # reverse the direction
        fake_trade_dict["executed_quantity"] = fake_trade_dict["total_quantity"]
        fake_trade_dict["timestamp"] = pd.datetime.now()
        if self.mode == "KRX":
            fake_trade_dict["timestamp"] = pd.datetime.now(
                pytz.timezone("Asia/Seoul")
            ).replace(tzinfo=None)
        elif self.mode == "US":
            fake_trade_dict["timestamp"] = pd.Timestamp(self.logit_date).replace(
                hour=22, minute=0
            )
        # display("FAKE :", fake_trade_dict)

        # FIXME : Repeats lines of handle
        trade_id = "10010" + str(trade_id)
        self.process_completed_trade(trade_id, fake_trade_dict)
        print("Removed Error Trade : ", trade_id)
        assert trade_id not in self.live

    def check_before_deleting(
        self, trade_id: str, trade_dict: dict
    ) -> Union[dict, bool]:
        """Function to check trade dict before deleting.

        Args:
            trade_id (str): tradeID
            trade_dict (dict): trade dict

        Returns:
            Union[dict, bool]: returns False if problem, else return trade dict
        """
        # Hack to get strategy name in rollover entries # FIXME : Really Terrible hack
        if trade_id[0] == "5" and trade_id[config.ENTRY_OR_EXIT] == "1":
            if int(trade_id[config.START_OF_ACTUAL_TRADE_ID :]) in self.live:
                trade_dict["strategy"] = self.live[
                    int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])
                ]["strategy"]
            elif int(trade_id[config.START_OF_ACTUAL_TRADE_ID :]) in self.dead:
                trade_dict["strategy"] = self.dead[
                    int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])
                ]["strategy"]
            else:
                print(
                    "Couldn't modify strategy name for this rollover entry %s", trade_id
                )

        if (
            trade_id[0] == "5"
            and trade_id[config.ENTRY_OR_EXIT] == "1"
            and int(trade_id[config.START_OF_ACTUAL_TRADE_ID :]) in self.live
        ):
            # This can happen when the execution of next expiry entry happens before current expiry has been exitted.
            self.buffered_entries[
                int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])
            ] = trade_dict
            del self.pending[int(trade_id)]
            return False

        elif (
            trade_id[config.ENTRY_OR_EXIT] == "0"
            and int(trade_id[config.START_OF_ACTUAL_TRADE_ID :]) not in self.live
        ):  # Exit without entry
            self.buffered_exits[
                int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])
            ] = trade_dict
            del self.pending[int(trade_id)]
            print("NoEntryForExit %s", trade_id)
            return False

        if trade_id[config.ENTRY_OR_EXIT] == "0" and self.live[
            int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])
        ]["total_quantity"] != (-1 * trade_dict["total_quantity"]):
            print("EntryExitQuantityMismatch %s", trade_id)
            return False

        if (
            trade_id[config.ENTRY_OR_EXIT] == "0"
            and trade_dict["expiry"] != "XX"
            and self.live[int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])]["expiry"]
            != trade_dict["expiry"]
        ):
            self.buffered_exits[
                int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])
            ] = trade_dict
            del self.pending[int(trade_id)]
            print("ExpiryMismatch %s", trade_id)
            return False
        else:
            return trade_dict

    def process_order(self, trade_id: str, order_dict: dict) -> Union[dict, bool]:
        """Function to process order.

        Description:
            * Process order. if problem, store order/trade in buffer

            * If last order of trade, return trade dict, else return False
        Args:
            trade_id (str): tradeID
            order_dict (dict): order dict

        Returns:
            Union[dict, bool]: returns False if problem, else return trade dict
        """
        if order_dict["quantity"] == 0:
            return False
        if int(trade_id) in self.pending:
            trade_dict = self.pending[int(trade_id)]
        else:
            if trade_id not in self.buffered_orders:  # Dont put in buffer twice
                self.buffered_orders[trade_id] = order_dict
                logging.critical("NoTradeForThisOrder %s", trade_id)
            else:
                self.buffered_orders[trade_id]["quantity"] += order_dict[
                    "quantity"
                ]  # TODO : Also find weighted price
                logging.critical("NoTradeForThisOrder %s", trade_id)
            return False

        tmp_num = (
            trade_dict["executed_quantity"] * trade_dict["weighted_price"]
            + order_dict["quantity"] * order_dict["execution_price"]
        )
        tmp_den = trade_dict["executed_quantity"] + order_dict["quantity"]
        trade_dict["weighted_price"] = tmp_num / tmp_den

        # Update trade_dict acc to quantity and execution_price
        trade_dict["executed_quantity"] += order_dict["quantity"]

        result = False
        if (
            trade_dict["executed_quantity"] == trade_dict["total_quantity"]
        ):  # Order has been completed
            result = self.check_before_deleting(
                trade_id, trade_dict
            )  # False or a trade_dict
            if result is not False:
                del self.pending[int(trade_id)]  # Remove from pending Orders

        return result

    def handle_entry(self, trade_id: str, trade_dict: dict):
        """Function to make entry into live.

        Args:
            trade_id (str): tradeID
            trade_dict (dict): trade dict
        """
        logit_trade_id = int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])
        del trade_dict["executed_quantity"]
        if logit_trade_id in self.live:
            logging.critical("DuplicateTradeLive %s", trade_id)
        self.live[logit_trade_id] = trade_dict
        if logit_trade_id in self.buffered_exits:
            trade_dict = self.buffered_exits[logit_trade_id]
            del self.buffered_exits[logit_trade_id]
            self.handle_exit(trade_id, trade_dict)

    def handle_exit(self, trade_id: str, trade_dict: dict):
        """Function to make trade go from live to dead.

        Args:
            trade_id (str): tradeID
            trade_dict (dict): trade dict
        """
        logit_trade_id = int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])
        exit_trade_dict = trade_dict
        entry_trade_dict = self.live[
            logit_trade_id
        ]  # Always works, because we buffer if entry absent for exit

        del self.live[logit_trade_id]  # remove from live

        # Make exit dict. Don't copy a dict and delete keys as it screws the ordering with live dict
        tmp_dict = dict()
        tmp_dict["entry_timestamp"] = entry_trade_dict["timestamp"]
        tmp_dict["segment"] = entry_trade_dict["segment"]
        tmp_dict["symbol"] = entry_trade_dict["symbol"]
        tmp_dict["expiry"] = entry_trade_dict["expiry"]
        tmp_dict["total_quantity"] = entry_trade_dict["total_quantity"]
        tmp_dict["strategy"] = entry_trade_dict["strategy"]
        tmp_dict["entry_price"] = entry_trade_dict["weighted_price"]
        tmp_dict["type"] = exit_trade_dict["type"]
        tmp_dict["strike"] = exit_trade_dict["strike"]
        tmp_dict["exit_timestamp"] = exit_trade_dict["timestamp"]
        tmp_dict["exit_price"] = exit_trade_dict["weighted_price"]
        try:
            if self.mode in ["NSE", "GIFT"]:
                strike = tmp_dict["strike"]
                if float(tmp_dict["strike"]).is_integer():
                    strike = str(int(float(tmp_dict["strike"])))
                contract_name = tmp_dict["symbol"] + tmp_dict["expiry"]
                contract_name += tmp_dict["type"] if tmp_dict["type"] != "XX" else ""
                contract_name += strike if strike != "0" else ""
                lot_size = self.store_lot_size[contract_name]
            else:
                symbol = (
                    trade_dict["symbol"]
                    if entry_trade_dict["segment"] not in ["OPTIDX_US"]
                    else trade_dict["symbol"] + "_OPTIDX"
                )
                lot_size = self.store_lot_size[symbol]
        except Exception as e:
            lot_size = None
        stt, brokerage, others = calc_charges(
            tmp_dict, logit_trade_id, lot_size, self.store_sharekhan
        )

        if logit_trade_id in self.dead:
            # Possible : A rolled over trade exited on SAME DAY of rollover
            if self.mode != "NSE":
                return
            logit_trade_id = logit_trade_id * 10  # Since duplicate keys are not allowed
            logging.critical(
                "DuplicateExit Added tradeid %s with new tradeid : %s"
                % (trade_id, logit_trade_id)
            )

        tmp_dict["stt"] = stt
        tmp_dict["brokerage"] = brokerage
        tmp_dict["others"] = others
        self.dead[logit_trade_id] = tmp_dict

        # Check if anything can be moved from buffered entries to live
        if logit_trade_id in self.buffered_entries:
            trade_dict = self.buffered_entries[logit_trade_id]
            del self.buffered_entries[logit_trade_id]
            del trade_dict["executed_quantity"]
            self.live[logit_trade_id] = trade_dict

    def handle_buffered_exits(self, trade_id: str):
        logit_trade_id = int(trade_id[config.START_OF_ACTUAL_TRADE_ID :])
        exit_trade_dict = self.buffered_exits[logit_trade_id]
        entry_trade_dict = self.buffered_entries[logit_trade_id]
        del self.buffered_exits[logit_trade_id]
        del self.buffered_entries[logit_trade_id]
        tmp_dict = dict()
        tmp_dict["entry_timestamp"] = entry_trade_dict["timestamp"]
        tmp_dict["segment"] = entry_trade_dict["segment"]
        tmp_dict["symbol"] = entry_trade_dict["symbol"]
        tmp_dict["expiry"] = entry_trade_dict["expiry"]
        tmp_dict["total_quantity"] = entry_trade_dict["total_quantity"]
        tmp_dict["strategy"] = entry_trade_dict["strategy"]
        tmp_dict["entry_price"] = entry_trade_dict["weighted_price"]
        tmp_dict["type"] = exit_trade_dict["type"]
        tmp_dict["strike"] = exit_trade_dict["strike"]
        tmp_dict["exit_timestamp"] = exit_trade_dict["timestamp"]
        tmp_dict["exit_price"] = exit_trade_dict["weighted_price"]
        try:
            if self.mode in ["NSE"]:
                strike = tmp_dict["strike"]
                if float(tmp_dict["strike"]).is_integer():
                    strike = str(int(float(tmp_dict["strike"])))
                contract_name = (
                    tmp_dict["symbol"] + tmp_dict["expiry"] + tmp_dict["type"] + strike
                )
                lot_size = self.store_lot_size[contract_name]
            else:
                symbol = (
                    tmp_dict["symbol"]
                    if entry_trade_dict["segment"] not in ["OPTIDX_US"]
                    else tmp_dict["symbol"] + "_OPTIDX"
                )
                lot_size = self.store_lot_size[symbol]
        except Exception as e:
            lot_size = None
        stt, brokerage, others = calc_charges(
            tmp_dict, logit_trade_id, lot_size, self.store_sharekhan
        )

        if logit_trade_id in self.dead:
            # Possible : A rolled over trade exited on SAME DAY of rollover
            logit_trade_id = logit_trade_id * 10  # Since duplicate keys are not allowed
            logging.critical(
                "DuplicateExit Added tradeid %s with new tradeid : %s"
                % (trade_id, logit_trade_id)
            )

        tmp_dict["stt"] = stt
        tmp_dict["brokerage"] = brokerage
        tmp_dict["others"] = others
        self.dead[logit_trade_id] = tmp_dict

    def process_completed_trade(self, trade_id: str, trade_dict: dict):
        """Function to move a trade from : pending -> live or live -> dead.

        Args:
            trade_id (str): tradeID
            trade_dict (dict): trade dict
        """
        if trade_id[config.ENTRY_OR_EXIT] == "1":
            self.handle_entry(trade_id, trade_dict)
        else:
            self.handle_exit(trade_id, trade_dict)

    def dump_to_csv_krx(self, location: str = "csv/"):
        """Function to dump krx object to csv.

        Args:
            location (str, optional): location where we want to dump. Defaults to "csv/".
        """
        tmp_df = pd.DataFrame.from_dict(self.dead, orient="index")
        tmp_df.to_csv(location + "krx_dead.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.live, orient="index")
        tmp_df.to_csv(location + "krx_live.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.pending, orient="index")
        tmp_df.to_csv(location + "krx_pending.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.buffered_exits, orient="index")
        tmp_df.to_csv(location + "krx_buffered.csv", index_label="logit_id")

    def dump_to_csv_us(self, location: str = "csv/"):
        """Function to dump us object to csv.

        Args:
            location (str, optional): location where we want to dump. Defaults to "csv/".
        """
        tmp_df = pd.DataFrame.from_dict(self.dead, orient="index")
        tmp_df.to_csv(location + "us_dead.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.live, orient="index")
        tmp_df.to_csv(location + "us_live.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.pending, orient="index")
        tmp_df.to_csv(location + "us_pending.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.buffered_exits, orient="index")
        tmp_df.to_csv(location + "us_buffered.csv", index_label="logit_id")

    def dump_to_csv_gift(self, location: str = "csv/"):
        """Function to dump gift object to csv.

        Args:
            location (str, optional): location where we want to dump. Defaults to "csv/".
        """
        tmp_df = pd.DataFrame.from_dict(self.dead, orient="index")
        tmp_df.to_csv(location + "gift_dead.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.live, orient="index")
        tmp_df.to_csv(location + "gift_live.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.pending, orient="index")
        tmp_df.to_csv(location + "gift_pending.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.buffered_exits, orient="index")
        tmp_df.to_csv(location + "gift_buffered.csv", index_label="logit_id")

    def dump_to_csv_mcx(self, location: str = "csv/"):
        """Function to dump mcx object to csv.

        Args:
            location (str, optional): location where we want to dump. Defaults to "csv/".
        """
        tmp_df = pd.DataFrame.from_dict(self.dead, orient="index")
        tmp_df.to_csv(location + "comm_dead.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.live, orient="index")
        tmp_df.to_csv(location + "comm_live.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.pending, orient="index")
        tmp_df.to_csv(location + "comm_pending.csv", index_label="logit_id")
        tmp_df = pd.DataFrame.from_dict(self.buffered_exits, orient="index")
        tmp_df.to_csv(location + "comm_buffered.csv", index_label="logit_id")

    def dump_to_csv(self, location: str = "csv/"):
        """Function to dump object to csv.

        Args:
            location (str, optional): location where we want to dump. Defaults to "csv/".
        """
        dump_to_csv(
            self.dead, self.live, self.pending, self.buffered_exits, location, self.key
        )

    def load_from_csv(self, location: str = "csv/"):
        """Function to load object from csv.

        Description:
            * Update the dead, live and pending using values in csv file.

            * Before updating , show the difference in values
        Args:
            location (str, optional): location where we load object from. Defaults to "csv/".
        """
        tmp_dead, tmp_live, tmp_pending, tmp_buffered = load_from_csv(location)

        compare_df(self.dead, tmp_dead, name1="old_dead", name2="changed_dead")
        compare_df(self.live, tmp_live, name1="old_live", name2="changed_live")
        compare_df(
            self.pending, tmp_pending, name1="old_pending", name2="changed_pending"
        )
        compare_df(
            self.buffered_exits,
            tmp_buffered,
            name1="old_buffered",
            name2="new_buffered",
        )
        self.dead, self.live, self.pending, self.buffered_exits = load_from_csv(
            location
        )

    def load_from_csv_df(self, identifier: str, location: str = "csv/"):
        """Function to load df from csv.

        Description:
            * Update the dead, live, pending or buffered df using values in csv file.

            * Before updating , show the difference in values
        Args:
            idenitfier (str): option to select which df to update.
            location (str, optional): location where we load object from. Defaults to "csv/".
        """
        tmp_dead, tmp_live, tmp_pending, tmp_buffered = load_from_csv(location)
        if identifier == "dead":
            compare_df(self.dead, tmp_dead, name1="old_dead", name2="changed_dead")
            self.dead = tmp_dead
        elif identifier == "live":
            compare_df(self.live, tmp_live, name1="old_live", name2="changed_live")
            self.live = tmp_live
        elif identifier == "pending":
            compare_df(
                self.pending, tmp_pending, name1="old_pending", name2="changed_pending"
            )
            self.pending = tmp_pending
        else:
            compare_df(
                self.buffered_exits,
                tmp_buffered,
                name1="old_buffered",
                name2="changed_buffered",
            )
            self.buffered_exits = tmp_buffered

    def load_from_csv_mcx(self, location: str = "csv/"):
        """Function to load object from csv for mcx.

        Description:
            * Update the dead, live and pending using values in csv file.

            * Before updating , show the difference in values
        Args:
            location (str, optional): location where we load object from. Defaults to "csv/".
        """
        tmp_dead, tmp_live, tmp_pending, tmp_buffered = load_from_csv_mcx(location)

        compare_df(self.dead, tmp_dead, name1="old_dead", name2="changed_dead")
        compare_df(self.live, tmp_live, name1="old_live", name2="changed_live")
        compare_df(
            self.pending, tmp_pending, name1="old_pending", name2="changed_pending"
        )
        compare_df(
            self.buffered_exits,
            tmp_buffered,
            name1="old_buffered",
            name2="new_buffered",
        )
        self.dead, self.live, self.pending, self.buffered_exits = load_from_csv_mcx(
            location
        )

    def load_from_csv_gift(self, location: str = "csv/"):
        """Function to load object from csv for gift.

        Description:
            * Update the dead, live and pending using values in csv file.

            * Before updating , show the difference in values
        Args:
            location (str, optional): location where we load object from. Defaults to "csv/".
        """
        tmp_dead, tmp_live, tmp_pending, tmp_buffered = load_from_csv_gift(location)

        compare_df(self.dead, tmp_dead, name1="old_dead", name2="changed_dead")
        compare_df(self.live, tmp_live, name1="old_live", name2="changed_live")
        compare_df(
            self.pending, tmp_pending, name1="old_pending", name2="changed_pending"
        )
        compare_df(
            self.buffered_exits,
            tmp_buffered,
            name1="old_buffered",
            name2="new_buffered",
        )
        self.dead, self.live, self.pending, self.buffered_exits = load_from_csv_gift(
            location
        )

    def load_from_csv_mcx_df(self, identifier: str, location: str = "csv/"):
        """Function to load df from csv for mcx.

        Description:
            * Update the dead, live or pending using values in csv file as specified by the identifier.

            * Before updating , show the difference in values
        Args:
            identifier (str): option to select which df to update
            location (str, optional): location where we load object from. Defaults to "csv/".
        """
        tmp_dead, tmp_live, tmp_pending, tmp_buffered = load_from_csv_mcx(location)

        if identifier == "dead":
            compare_df(self.dead, tmp_dead, name1="old_dead", name2="changed_dead")
            self.dead = tmp_dead
        elif identifier == "live":
            compare_df(self.live, tmp_live, name1="old_live", name2="changed_live")
            self.live = tmp_live
        elif identifier == "pending":
            compare_df(
                self.pending, tmp_pending, name1="old_pending", name2="changed_pending"
            )
            self.pending = tmp_pending
        else:
            compare_df(
                self.buffered_exits,
                tmp_buffered,
                name1="old_buffered",
                name2="new_buffered",
            )
            self.buffered_exits = tmp_buffered

    def load_from_csv_krx(self, location: str = "csv/"):
        """Function to load object from csv for krx.

        Description:
            * Update the dead, live and pending using values in csv file.

            * Before updating , show the difference in values
        Args:
            location (str, optional): location where we load object from. Defaults to "csv/".
        """
        tmp_dead, tmp_live, tmp_pending, tmp_buffered = load_from_csv_krx(location)

        compare_df(self.dead, tmp_dead, name1="old_dead", name2="changed_dead")
        compare_df(self.live, tmp_live, name1="old_live", name2="changed_live")
        compare_df(
            self.pending, tmp_pending, name1="old_pending", name2="changed_pending"
        )
        compare_df(
            self.buffered_exits,
            tmp_buffered,
            name1="old_buffered",
            name2="new_buffered",
        )
        self.dead, self.live, self.pending, self.buffered_exits = load_from_csv_krx(
            location
        )

    def load_from_csv_krx_df(self, identifier: str, location: str = "csv/"):
        """Function to load df from csv for krx.

        Description:
            * Update the dead, live or pending using values in csv file as specified by the identifier.

            * Before updating , show the difference in values
        Args:
            identifier (str): option to select which df to update
            location (str, optional): location where we load object from. Defaults to "csv/".
        """
        tmp_dead, tmp_live, tmp_pending, tmp_buffered = load_from_csv_krx(location)

        if identifier == "dead":
            compare_df(self.dead, tmp_dead, name1="old_dead", name2="changed_dead")
            self.dead = tmp_dead
        elif identifier == "live":
            compare_df(self.live, tmp_live, name1="old_live", name2="changed_live")
            self.live = tmp_live
        elif identifier == "pending":
            compare_df(
                self.pending, tmp_pending, name1="old_pending", name2="changed_pending"
            )
            self.pending = tmp_pending
        else:
            compare_df(
                self.buffered_exits,
                tmp_buffered,
                name1="old_buffered",
                name2="new_buffered",
            )
            self.buffered_exits = tmp_buffered

    def load_from_csv_us_df(self, identifier: str, location: str = "csv/"):
        """Function to load df from csv for us.

        Description:
            * Update the dead, live or pending using values in csv file as specified by the identifier.

            * Before updating , show the difference in values
        Args:
            identifier (str): option to select which df to update
            location (str, optional): location where we load object from. Defaults to "csv/".
        """
        tmp_dead, tmp_live, tmp_pending, tmp_buffered = load_from_csv_us(location)

        if identifier == "dead":
            compare_df(self.dead, tmp_dead, name1="old_dead", name2="changed_dead")
            self.dead = tmp_dead
        elif identifier == "live":
            compare_df(self.live, tmp_live, name1="old_live", name2="changed_live")
            self.live = tmp_live
        elif identifier == "pending":
            compare_df(
                self.pending, tmp_pending, name1="old_pending", name2="changed_pending"
            )
            self.pending = tmp_pending
        else:
            compare_df(
                self.buffered_exits,
                tmp_buffered,
                name1="old_buffered",
                name2="new_buffered",
            )
            self.buffered_exits = tmp_buffered

    def load_from_csv_us(self, location: str = "csv/"):
        """Function to load object from csv for us.

        Description:
            * Update the dead, live and pending using values in csv file.

            * Before updating , show the difference in values
        Args:
            location (str, optional): location where we load object from. Defaults to "csv/".
        """
        tmp_dead, tmp_live, tmp_pending, tmp_buffered = load_from_csv_us(location)

        compare_df(self.dead, tmp_dead, name1="old_dead", name2="changed_dead")
        compare_df(self.live, tmp_live, name1="old_live", name2="changed_live")
        compare_df(
            self.pending, tmp_pending, name1="old_pending", name2="changed_pending"
        )
        compare_df(
            self.buffered_exits,
            tmp_buffered,
            name1="old_buffered",
            name2="new_buffered",
        )
        self.dead, self.live, self.pending, self.buffered_exits = load_from_csv_us(
            location
        )
