# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import logit.sender_pb2 as sender__pb2


class ChunkerStub(object):
    # missing associated documentation comment in .proto file
    pass

    def __init__(self, channel):
        """Constructor.

        Args:
          channel: A grpc.Channel.
        """
        self.get_data = channel.unary_unary(
            "/chunkerservice.Chunker/get_data",
            request_serializer=sender__pb2.ChunkerRequest.SerializeToString,
            response_deserializer=sender__pb2.ResponseArray.FromString,
        )
        self.get_latest_timestamp = channel.unary_unary(
            "/chunkerservice.Chunker/get_latest_timestamp",
            request_serializer=sender__pb2.TimeStampRequest.SerializeToString,
            response_deserializer=sender__pb2.TimestampResponse.FromString,
        )
        self.get_all_data = channel.unary_unary(
            "/chunkerservice.Chunker/get_all_data",
            request_serializer=sender__pb2.TimestampResponse.SerializeToString,
            response_deserializer=sender__pb2.AllResponseArray.FromString,
        )


class ChunkerServicer(object):
    # missing associated documentation comment in .proto file
    pass

    def get_data(self, request, context):
        # missing associated documentation comment in .proto file
        pass
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def get_latest_timestamp(self, request, context):
        # missing associated documentation comment in .proto file
        pass
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def get_all_data(self, request, context):
        # missing associated documentation comment in .proto file
        pass
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_ChunkerServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "get_data": grpc.unary_unary_rpc_method_handler(
            servicer.get_data,
            request_deserializer=sender__pb2.ChunkerRequest.FromString,
            response_serializer=sender__pb2.ResponseArray.SerializeToString,
        ),
        "get_latest_timestamp": grpc.unary_unary_rpc_method_handler(
            servicer.get_latest_timestamp,
            request_deserializer=sender__pb2.TimeStampRequest.FromString,
            response_serializer=sender__pb2.TimestampResponse.SerializeToString,
        ),
        "get_all_data": grpc.unary_unary_rpc_method_handler(
            servicer.get_all_data,
            request_deserializer=sender__pb2.TimestampResponse.FromString,
            response_serializer=sender__pb2.AllResponseArray.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "chunkerservice.Chunker", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))
