from typing import Tuple
import logit.config as config


def parse_execution_logs(message_dict: dict):
    order_dict = message_dict["execution_log"]
    order_info = {}
    order_info["timestamp"] = message_dict["timestamp"]
    order_info["netoff"] = "NetOff" if "netoff" in order_dict else ""
    order_info["symbol"] = order_dict["contract"]
    order_info["quantity"] = order_dict["trade_qty"]
    if "KOSPI" in order_info["symbol"]:
        order_info["execution_price"] = order_dict["executed_price"] / 100000
    else:
        order_info["execution_price"] = order_dict["executed_price"] / 100
    if ("buy_sell" in order_dict) and (order_dict["buy_sell"] in ["sell"]):
        order_info["quantity"] *= -1
    return order_dict["trade_id"], order_info


def parse_new_order_logs(message_dict: dict, store_sharekhan: dict):
    order_dict = message_dict["new_order_log"]
    order_info = {}
    try:
        store_sharekhan[int(str(order_dict["trade_id"][5:]))] = int(
            order_dict.get("system_id", "0")
        )
    except Exception as e:
        print(f"Failed at parsing new order log due to {repr(e)}")
        raise e
    order_info["timestamp"] = message_dict["timestamp"]
    order_info["segment"] = order_dict["instrument"]
    order_info["symbol"] = order_dict["symbol"]
    order_info["expiry"] = order_dict["expiry_date"]
    order_info["total_quantity"] = order_dict["trade_qty"]
    if ("buy_sell" in order_dict) and (order_dict["buy_sell"] in ["sell", "1", 1]):
        order_info["total_quantity"] *= -1
    order_info["strategy"] = order_dict.get("strategy_name", "dummy_strategy")
    order_info["weighted_price"] = 0.0
    order_info["type"] = order_dict["option_type"]
    order_info["strike"] = str(order_dict.get("strike", "0"))
    order_info["executed_quantity"] = 0.0
    return order_dict["trade_id"], order_info


def parse_contract(contract: str) -> Tuple[str, str, str, str]:
    """Returns symbol, expiry, option_type, strike of given contract.
        If some values are not available, empty string is returned

    Args:
        contract (`str`): Contract to be parsed

    Returns:
        `str`, `str`, `str`, `str`: symbol, expiry, option_type, strike respectively
    """
    symbol = ""
    option_type = ""
    expiry = ""
    strike = ""
    for mon in config.MONTHS:
        tmp = "-" + mon + "-"
        if tmp in contract:
            idx = contract.index(tmp)
            symbol = contract[: idx - 2]
            expiry = contract[idx - 2 : idx + 9]
            option_type = contract[idx + 9 : idx + 11]
            strike = contract[idx + 11 :]
            break
    if symbol == "":
        symbol = contract

    return symbol, expiry, option_type, strike
