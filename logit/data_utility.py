import pandas as pd
import numpy as np
import datetime as dt
import time
import logit.sender_pb2 as sender_pb2
import logit.sender_pb2_grpc as sender_pb2_grpc
import grpc
import time
import logging
import logit.config as config
from io import BytesIO
from datetime import datetime
from typing import Optional

stub = grpc.insecure_channel(config.STUB)
grpc_client = sender_pb2_grpc.ChunkerStub(stub)
stub_xn = grpc.insecure_channel(config.STUB_XN)
grpc_client_xn = sender_pb2_grpc.ChunkerStub(stub_xn)
stub_mcx = grpc.insecure_channel(config.STUB_MCX)
grpc_client_mcx = sender_pb2_grpc.ChunkerStub(stub_mcx)
stub_ncdex = grpc.insecure_channel(config.STUB_NCDEX)
grpc_client_ncdex = sender_pb2_grpc.ChunkerStub(stub_ncdex)
stub_gift = grpc.insecure_channel(config.STUB_GIFT)
grpc_client_gift = sender_pb2_grpc.ChunkerStub(stub_gift)
stub_bse = grpc.insecure_channel(config.STUB_BSE)
grpc_client_bse = sender_pb2_grpc.ChunkerStub(stub_bse)


grpc_server = {}
grpc_server["NF"] = grpc_client
grpc_server["CASH"] = grpc_client
grpc_server["XN"] = grpc_client_xn
grpc_server["MCX"] = grpc_client_mcx
grpc_server["NCDEX"] = grpc_client_ncdex
grpc_server["GIFT_NF"] = grpc_client_gift
grpc_server["BSE_NF"] = grpc_client_bse


def get_data(tm: pd.Timestamp, segment: str) -> pd.DataFrame:
    """Function to get data for a given timestamp and segment.

    Args:
        tm (pd.Timestamp): timestamp
        segment (str): segment name

    Returns:
        pd.DataFrame: ltp for the given segment & timestamp
    """
    data1min = {}
    if segment in ["GIFT_NF"]:
        tm = tm - pd.Timedelta(hours=3)
    if (tm.time() >= dt.time(15, 30)) & (segment in ["NF", "CASH", "BSE_NF"]):
        tm = pd.Timestamp(year=tm.year, month=tm.month, day=tm.day, hour=15, minute=30)
    elif (tm.time() >= dt.time(17, 0)) & (segment in ["XN", "NCDEX"]):
        tm = pd.Timestamp(year=tm.year, month=tm.month, day=tm.day, hour=17, minute=0)
    elif (tm.time() >= dt.time(23, 45)) & (segment in ["GIFT_NF"]):
        tm = pd.Timestamp(year=tm.year, month=tm.month, day=tm.day, hour=23, minute=45)
    else:
        tm = pd.Timestamp(
            year=tm.year, month=tm.month, day=tm.day, hour=tm.hour, minute=tm.minute
        )
    if (pd.Timestamp.now() - tm).total_seconds() < 1:
        time.sleep(1)
    count_failed_attempts = 0
    while True:
        try:
            data = grpc_server[segment].get_data(
                sender_pb2.ChunkerRequest(
                    segment=segment.lstrip("GIFT_").lstrip("BSE_"),
                    freq="1",
                    dtype="trd",
                    timestamp=tm.strftime("%Y-%m-%d %H:%M:%S"),
                )
            )
            break
        except grpc.RpcError as e:
            count_failed_attempts += 1
            if count_failed_attempts >= config.FAILED_ATTEMPTS_THRESHOLD:
                logging.error(
                    "GetFresh{} FAILED for {} @ {} with ERROR:: {}: {}..Retrying in {} sec".format(
                        1,
                        segment,
                        tm.strftime("%Y-%m-%d %H:%M:%S"),
                        e.code(),
                        e.details(),
                        config.SLEEP_TIME,
                    )
                )
                time.sleep(config.SLEEP_TIME)
    for s in data.message:
        flds = s.split("|")
        if len(flds) != 8 and segment not in ["GIFT_NF", "BSE_NF"]:
            continue
        else:
            data1min[flds[0]] = {
                "open": float(flds[1]) / 100,
                "high": float(flds[2]) / 100,
                "low": float(flds[3]) / 100,
                "close": float(flds[4]) / 100,
                "volume": float(flds[5]),
                "ltp": np.nan,
            }
    data1min = pd.DataFrame.from_dict(data1min, orient="index")
    data1min = data1min.reset_index()
    if len(data1min) != 0:
        data1min = data1min.loc[:, ["index", "close", "volume"]]
        data1min.columns = ["contract", "ltp", "cons_volume"]
    return data1min


def generate_contract_name_krx(balte_id: int, symbol: str) -> str:
    """
    Returns the contract name string from balte_id and symbol
    """
    # For futures contract
    if ((balte_id // (1e4)) % 10) == 2:
        expiry = expiry = datetime.strptime(
            str(int((balte_id) / 1e5)), "%Y%m%d"
        ).strftime("%d-%b-%Y")
        contract_name = symbol + "_" + expiry
        return contract_name
    # For options contract
    option_type = "CE" if ((balte_id // (1e4)) % 10) == 1 else "PE"
    expiry = datetime.strptime(str(int((balte_id % 1e13) / 1e5)), "%Y%m%d").strftime(
        "%d-%b-%Y"
    )
    strike = int(balte_id / int(1e13))
    contract_name = symbol + "_" + expiry + "_" + option_type + "_" + str(strike)
    return contract_name


def generate_contract_name_us(balte_id: int) -> str:
    """
    Returns the contract name string from balte_id
    """
    # For options contract
    option_type = "CE" if ((balte_id // (1e4)) % 10) == 1 else "PE"
    expiry = datetime.strptime(str(int((balte_id % 1e13) / 1e5)), "%Y%m%d").strftime(
        "%d-%b-%Y"
    )
    strike = int(balte_id / int(1e15))
    underlying_balte_id: int = int(balte_id % 10_000)
    symbol = config.balte_id_to_symbol[underlying_balte_id]
    contract_name = symbol + expiry + option_type + str(strike)
    return contract_name


def get_data_international(
    tm: pd.Timestamp, segment: str, logit_date: Optional[pd.Timestamp] = None
) -> pd.DataFrame:
    """Function to get data for a given timestamp and segment.

    Args:
        tm (pd.Timestamp): timestamp
        segment (str): segment name

    Returns:
        pd.DataFrame: ltp for the given segment & timestamp
    """

    if segment == "OPTIDX_KRX":
        df = pd.read_csv(
            BytesIO(
                config.minioClient.get_object(
                    config.MINIO_KRX_DAILY_DATA_FILE_BUCKET_NAME,
                    config.MINIO_LIVE_DATA_LOCATION["optidx_1_min_krx"],
                ).data
            ),
            header=None,
            names=["timestamp", "ID", "Open", "High", "Low", "Close", "Cons_Volume"],
        )
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        fresh_df = df[(df["timestamp"] == tm)]
        fresh_df = fresh_df[~(fresh_df["ID"].isin([8001, 8007]))]
        fresh_df["contract"] = fresh_df["ID"].apply(
            lambda x: generate_contract_name_krx(balte_id=x, symbol="KOSPI")
        )
    elif segment == "FUTIDX_KRX":
        df = pd.read_csv(
            BytesIO(
                config.minioClient.get_object(
                    config.MINIO_KRX_DAILY_DATA_FILE_BUCKET_NAME,
                    config.MINIO_LIVE_DATA_LOCATION["futidx_fut_1_min_krx"],
                ).data
            ),
            header=None,
            names=[
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "Next_Close",
                "Next_Cons_Volume",
            ],
        )
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        fresh_df = df[(df["timestamp"] == tm)]
        from datetime import datetime

        ## assuming near future contracts only in trade
        expiry = config.KOSPI_FUT_EXPIRY_DICT[pd.Timestamp.now().normalize()][
            "near"
        ].strftime("%d-%b-%Y")
        balte_id = int(
            datetime.strptime(expiry, "%d-%b-%Y").strftime("%Y%m%d")
            + "2"
            + str(8001).rjust(4, "0")
        )
        fresh_df["ID"] = balte_id
        fresh_df["contract"] = fresh_df["ID"].apply(
            lambda x: generate_contract_name_krx(balte_id=x, symbol="KOSPI")
        )
    elif segment == "FUTIDX_US":
        df = pd.read_csv(
            BytesIO(
                config.minioClient.get_object(
                    config.MINIO_US_DAILY_DATA_FILE_BUCKET_NAME,
                    config.MINIO_LIVE_DATA_LOCATION["futidx_fut_1_min_us"],
                ).data
            ),
            header=None,
            names=[
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "Next_Close",
                "Next_Cons_Volume",
            ],
        )
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        fresh_df = df
        if len(fresh_df) == 0:
            return pd.DataFrame()
        ## assuming near future contracts only in trade
        tmp_expiry_dict = {}
        for _id in config.US_FUT_EXPIRY_DICT:
            tmp_expiry_dict[_id] = config.US_FUT_EXPIRY_DICT[_id][
                pd.Timestamp(logit_date)
            ]["near_month"]
        near_exp_dict = pd.DataFrame.from_dict(tmp_expiry_dict, orient="index")
        near_exp_dict.rename_axis("balte_id", inplace=True)
        near_exp_dict.rename(columns={0: "expiry"}, inplace=True)
        fresh_df = fresh_df.merge(near_exp_dict, left_on="ID", right_on="balte_id")
        from datetime import datetime

        def convert_contract_name(row):
            expiry_str = row["expiry"].strftime(
                "%d-%b-%Y"
            )  # Convert Timestamp to string
            symbol: str = config.balte_id_to_symbol[int(row["ID"])]
            return symbol + expiry_str

        fresh_df["contract"] = fresh_df.apply(convert_contract_name, axis=1)
    elif segment == "OPTIDX_US":
        df = pd.read_csv(
            BytesIO(
                config.minioClient.get_object(
                    config.MINIO_US_DAILY_DATA_FILE_BUCKET_NAME,
                    config.MINIO_LIVE_DATA_LOCATION["optidx_1_min_us"],
                ).data
            ),
            header=None,
            names=["timestamp", "ID", "Open", "High", "Low", "Close", "Cons_Volume"],
        )
        df["timestamp"] = pd.to_datetime(df["timestamp"])
        fresh_df = df
        if len(fresh_df) == 0:
            return pd.DataFrame()
        fresh_df["contract"] = fresh_df["ID"].apply(
            lambda x: generate_contract_name_us(balte_id=x)
        )
    fresh_df = fresh_df[["contract", "Close", "Cons_Volume"]]
    fresh_df.rename(
        columns={"Close": "ltp", "Cons_Volume": "cons_volume"}, inplace=True
    )
    return fresh_df


def get_ltp(
    segment: str, hour1: str, minute1: str, logit_date: Optional[pd.Timestamp] = None
) -> pd.DataFrame:
    """Function to get last traded price.

    Args:
        segment (str): segment name
        hour1 (str): hour
        minute1 (str): minute

    Returns:
        pd.DataFrame: last traded price for all symbols.
    """
    hour1 = int(hour1)
    minute1 = int(minute1)
    timestamp = dt.datetime(
        dt.datetime.now().year,
        dt.datetime.now().month,
        dt.datetime.now().day,
        hour1,
        minute1,
    )
    if segment in ["FUTIDX_US", "OPTIDX_US"]:
        timestamp = pd.Timestamp(logit_date).replace(hour=hour1, minute=minute1)
    df_close = pd.DataFrame()
    if segment not in ["OPTIDX_KRX", "FUTIDX_KRX"]:
        if timestamp.time() < dt.time(3, 30):
            return df_close
        if segment in config.SEGMENTS["NSE"] and timestamp.time() < dt.time(9, 16):
            return df_close
    elif segment not in ["OPTIDX_US", "FUTIDX_US"]:
        if timestamp.time() < dt.time(9, 1):
            return df_close
    if segment in ["NCDEX"]:
        if timestamp.time() < dt.time(10, 5):
            return df_close
        tm = pd.Timestamp(
            dt.datetime.now().year,
            dt.datetime.now().month,
            dt.datetime.now().day,
            hour1,
            minute1,
        )
        _timestamp = tm.round("5T")
        if _timestamp > tm:
            _timestamp -= pd.Timedelta(minutes=5)
        timestamp = _timestamp
    if segment not in ["OPTIDX_KRX", "FUTIDX_KRX", "FUTIDX_US", "OPTIDX_US"]:
        df_close = get_data(timestamp, segment)
    else:
        df_close = get_data_international(timestamp, segment, logit_date)
    if (segment in ["XN", "GIFT_NF"]) and (len(df_close)):
        df_close["ltp"] /= 100
    if segment == "NCDEX":
        df_close["ltp"] *= 100
    return df_close.reset_index(drop=True)
