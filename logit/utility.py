import logging
import pandas as pd
import datetime as dt
import sqlalchemy as sa
from IPython.display import display, clear_output
import datetime
from datetime import datetime, timedelta
import numpy as np
from typing import List, Tuple, Union
import logit.config as config
from io import BytesIO
import copy
import requests
import os
from logit.config import TRANSACTION_CHARGES_US


def cstr(text: str, color: str = "black", size: str = "h4") -> str:
    """Function to generate html code for headings.

    Args:
        text (str): text to display
        color (str, optional): color of text. Defaults to "black".
        size (str, optional): size of text. Defaults to "h4".

    Returns:
        str: html code string
    """
    return "<{} style=color:{}>{}</{}>".format(size, color, text, size)


def compare_dataframe(
    df1: pd.DataFrame,
    df2: pd.DataFrame,
    col_df1: List,
    col_df2: List,
    fill_nan_with: int = 0,
    precision: List[int] = None,
    return_full_df: bool = False,
    ignore: List = [],
    return_value: bool = False,
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Function to compare given dataframes.

    Args:
        df1 (pd.DataFrame): first df
        df2 (pd.DataFrame): second df
        col_df1 (List): cols to consider in first
        col_df2 (List): cols to consider in second
        fill_nan_with (int, optional): values to fill instead of nan. Defaults to 0.
        precision (List[int], optional): precision to which we compare. Defaults to None.
        return_full_df (bool, optional): specifies if we return full df. Defaults to False.
        ignore (List, optional): rows to ignore. Defaults to [].
        return_value (bool, optional): specified if we return value or not. Defaults to False.

    Raises:
        ValueError: if length of columns to compare does not match

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: difference in df is we return values
    """
    if len(col_df1) != len(col_df2):
        raise ValueError("Length of columns to compare dont match")

    if precision is None:
        precision = [0] * len(col_df1)

    absent = []
    only_df1 = df1[~df1.index.isin(df2.index)]
    only_df1 = only_df1[~only_df1.index.isin(ignore.astype("uint64"))]
    if len(only_df1) != 0:
        if return_value is False:
            print("Only In Logger")
            display(only_df1)
        absent.extend(list(only_df1.index))

    only_df2 = df2[~df2.index.isin(df1.index)]
    only_df2 = only_df2[only_df2[8] != 0]
    only_df2 = only_df2[~only_df2.index.isin(ignore.astype("uint64"))]
    if len(only_df2) != 0:
        if return_value is False:
            print("Only in Matlab")
            display(only_df2)
        absent.extend(only_df2.index)

    df_joined = df1.join(df2, how="outer")
    df_joined = df_joined[~df_joined.index.isin(absent)]
    df_joined = df_joined[~df_joined.index.isin(ignore.astype("uint64"))]
    df_joined = df_joined.fillna(fill_nan_with)

    result_holder = []
    for i, j, prec in zip(col_df1, col_df2, precision):
        if return_full_df:
            df_diff = df_joined[abs(df_joined[i] - df_joined[j]) > prec]
        else:
            df_diff = df_joined[abs(df_joined[i] - df_joined[j]) > prec][[i, j]]
        if len(df_diff) != 0:
            if return_value is False:
                print("Quantity Mismatch:")
                display(df_diff)
        result_holder.append(df_diff)

    if return_value:
        return only_df1, only_df2

    return


def compare_df(
    obj1: Union[dict, pd.DataFrame],
    obj2: Union[dict, pd.DataFrame],
    name1: str = "First DataFrame",
    name2: str = "Second DataFrame",
):
    """Function to compare 2 dataframes.

    Args:
        obj1 (Union[dict, pd.DataFrame]): first dict or df
        obj2 (Union[dict, pd.DataFrame]): second dict or df
        name1 (str, optional): name of first df. Defaults to "First DataFrame".
        name2 (str, optional): name of second df. Defaults to "Second DataFrame".
    """

    if type(obj1) == dict:
        obj1 = pd.DataFrame.from_dict(obj1, orient="index")
        obj2 = pd.DataFrame.from_dict(obj2, orient="index")

    tmp = obj1[~obj1.index.isin(obj2.index)]
    if len(tmp) != 0:
        print("Only in ", name1)
        display(tmp)
    tmp = obj2[~obj2.index.isin(obj1.index)]
    if len(tmp) != 0:
        print("Only in ", name2)
        display(tmp)


def compare_df_with_quantities(
    obj1: pd.DataFrame,
    obj2: pd.DataFrame,
    name1: str = "First Dataframe",
    name2: str = "Second dataframe",
    col_to_compare: str = "total_quantity",
    return_value: bool = False,
) -> pd.DataFrame:
    """Function to compare df with quantites.

    Description:
        Conditions for Red Flag:

        * Our nan and their non zero
        * Our non zero and their zero
        * Their nan and out non zero
    Args:
        obj1 (pd.DataFrame): first df
        obj2 (pd.DataFrame): second df
        name1 (str, optional): name of first df. Defaults to "First Dataframe".
        name2 (str, optional): name of second df. Defaults to "Second dataframe".
        col_to_compare (str, optional): col to compare. Defaults to "total_quantity".
        return_value (bool, optional): specifies if we return value. Defaults to False.

    Returns:
        pd.DataFrame: the difference if we return value
    """
    tmp1 = obj1.join(obj2, how="outer", lsuffix=name1, rsuffix=name2)
    tmp1[col_to_compare + name1] = tmp1[col_to_compare + name1].fillna(0)
    tmp1[col_to_compare + name2] = tmp1[col_to_compare + name2].fillna(0)
    tmp1 = tmp1[
        ~((tmp1[col_to_compare + name1] == 0) & (tmp1[col_to_compare + name2] == 0))
    ]  # Eliminate 0,0
    if return_value is False:
        display(tmp1[tmp1[col_to_compare + name1] != tmp1[col_to_compare + name2]])
    else:
        return tmp1[tmp1[col_to_compare + name1] != tmp1[col_to_compare + name2]]


def show_intraday_cash(df: pd.DataFrame):
    """Function to show intraday cash positions at eod.

    Args:
        df (pd.DataFrame): pending orders df
    """
    if datetime.datetime.now().time() < datetime.time(15, 20):
        return
    if len(df) == 0:
        print("No remaining buys for cash stocks that were SS before 15:00")
        return
    df_only_ss = df[df.timestamp.dt.time < pd.Timestamp(2019, 1, 1, 15, 0).time()]
    clear_output(wait=True)
    tmp = df_only_ss[df_only_ss.segment == "CASH"][
        ["symbol", "total_quantity", "executed_quantity"]
    ]
    display(tmp.sort_values("total_quantity"))


def get_today_pnl(
    dead: dict,
    diagnose: bool = False,
    diagnose_entry_exit_price_diff_limit: int = 100,
    cur_flag: bool = False,
) -> float:
    """Function to get today's pnl.

    Args:
        dead (dict): dead orders
        diagnose (bool, optional): specifies if we perform temporary diagnosis. Defaults to False.
        diagnose_entry_exit_price_diff_limit (int, optional): threshold difference. Defaults to 100.
        cur_flag (bool, optional): currency flag. Defaults to False.

    Returns:
        float: _description_
    """

    def tmp_diagnose(key: str, value: dict):
        """Internal function to perform temporary diagnosis on given and values.

        Args:
            key (str): given key
            value (dict): given value dict (dead order)
        """

        if value["total_quantity"] == 0:
            print("ZeroQuantityTrade ", key)

        if value["entry_price"] == 0 or value["exit_price"] == 0:
            print("EntryOrExitPriceZero ", key)

        if (
            abs(value["entry_price"] - value["exit_price"])
            >= diagnose_entry_exit_price_diff_limit
        ):
            print("Warning..diff in entry/exit more than 50 ", key)

        if value["stt"] <= 0 or value["brokerage"] <= 0 or value["others"] <= 0:
            print("SomeChargesAreZero ", key)

    today_pnl = 0
    today_pnl_cur = 0
    for key, value in dead.items():
        if diagnose:
            tmp_diagnose(key, value)

        if np.isnan(value["entry_price"]):
            print("Encountered NaN in Entry Price", key)
            continue
        quantity = value["total_quantity"]
        if value["segment"] == "FUTCUR" or value["segment"] == "OPTCUR":
            quantity *= 1000
            today_pnl_cur += quantity * (value["exit_price"] - value["entry_price"]) - (
                value["stt"] + value["brokerage"] + value["others"]
            )
        else:
            today_pnl += quantity * (value["exit_price"] - value["entry_price"]) - (
                value["stt"] + value["brokerage"] + value["others"]
            )

    if cur_flag:
        today_pnl = today_pnl_cur

    return today_pnl


def get_today_pnl_comm(
    dead: dict,
    multiplier: dict,
    diagnose: bool = False,
    diagnose_entry_exit_price_diff_limit: int = 100,
) -> float:
    """Function to get today's pnl for commodities (mcx & ncdex).

    Args:
        dead (dict): dead order dict
        multiplier (dict): lotsize information dict
        diagnose (bool, optional): specifies if we want temporary diagnosis. Defaults to False.
        diagnose_entry_exit_price_diff_limit (int, optional): threshold difference. Defaults to 100.

    Returns:
        float: today's pnl
    """

    def tmp_diagnose(key: str, value: dict):
        """Internal function to perform temporary diagnosis on given and values.

        Args:
            key (str): given key
            value (dict): given value dict (dead order)
        """

        if value["total_quantity"] == 0:
            print("ZeroQuantityTrade ", key)

        if value["entry_price"] == 0 or value["exit_price"] == 0:
            print("EntryOrExitPriceZero ", key)

        if (
            abs(value["entry_price"] - value["exit_price"])
            >= diagnose_entry_exit_price_diff_limit
        ):
            print("Warning..diff in entry/exit more than 50 ", key)

        if value["stt"] <= 0 or value["brokerage"] <= 0 or value["others"] <= 0:
            print("SomeChargesAreZero ", key)

    today_pnl = 0
    for key, value in dead.items():
        if diagnose:
            tmp_diagnose(key, value)

        if np.isnan(value["entry_price"]):
            print("Encountered NaN in Entry Price", key)
            continue
        today_pnl += value["total_quantity"] * (
            value["exit_price"] - value["entry_price"]
        ) * multiplier[value["symbol"]] - (
            value["stt"] + value["brokerage"] + value["others"]
        )
    return today_pnl


def get_today_pnl_gift(
    dead: dict,
    multiplier: dict,
    diagnose: bool = False,
    diagnose_entry_exit_price_diff_limit: int = 100,
) -> float:
    """Function to get today's pnl for gift exchange.

    Args:
        dead (dict): dead order dict
        multiplier (dict): lotsize information dict
        diagnose (bool, optional): specifies if we want temporary diagnosis. Defaults to False.
        diagnose_entry_exit_price_diff_limit (int, optional): threshold difference. Defaults to 100.

    Returns:
        float: today's pnl
    """

    def tmp_diagnose(key: str, value: dict):
        """Internal function to perform temporary diagnosis on given and values.

        Args:
            key (str): given key
            value (dict): given value dict (dead order)
        """

        if value["total_quantity"] == 0:
            print("ZeroQuantityTrade ", key)

        if value["entry_price"] == 0 or value["exit_price"] == 0:
            print("EntryOrExitPriceZero ", key)

        if (
            abs(value["entry_price"] - value["exit_price"])
            >= diagnose_entry_exit_price_diff_limit
        ):
            print("Warning..diff in entry/exit more than 50 ", key)

        if value["stt"] <= 0 or value["brokerage"] <= 0 or value["others"] <= 0:
            print("SomeChargesAreZero ", key)

    today_pnl = 0
    for key, value in dead.items():
        if diagnose:
            tmp_diagnose(key, value)

        if np.isnan(value["entry_price"]):
            print("Encountered NaN in Entry Price", key)
            continue
        tmp = value["symbol"] + value["expiry"].strftime("%d-%b-%Y")
        today_pnl += value["total_quantity"] * (
            value["exit_price"] - value["entry_price"]
        ) - (value["stt"] + value["brokerage"] + value["others"])
    return today_pnl


def get_today_pnl_us(
    dead: dict,
    diagnose: bool = False,
    diagnose_entry_exit_price_diff_limit: int = 100,
) -> float:
    """Function to get today's pnl for us.

    Args:
        dead (dict): dead order dict
        diagnose (bool, optional): specifies if we want temporary diagnosis. Defaults to False.
        diagnose_entry_exit_price_diff_limit (int, optional): threshold difference. Defaults to 100.

    Returns:
        float: today's pnl
    """

    def tmp_diagnose(key: str, value: dict):
        """Internal function to perform temporary diagnosis on given and values.

        Args:
            key (str): given key
            value (dict): given value dict (dead order)
        """

        if value["total_quantity"] == 0:
            print("ZeroQuantityTrade ", key)

        if value["entry_price"] == 0 or value["exit_price"] == 0:
            print("EntryOrExitPriceZero ", key)

        if (
            abs(value["entry_price"] - value["exit_price"])
            >= diagnose_entry_exit_price_diff_limit
        ):
            print("Warning..diff in entry/exit more than 50 ", key)

        if value["stt"] <= 0 or value["brokerage"] <= 0 or value["others"] <= 0:
            print("SomeChargesAreZero ", key)

    today_pnl = 0
    for key, value in dead.items():
        if diagnose:
            tmp_diagnose(key, value)

        if np.isnan(value["entry_price"]):
            print("Encountered NaN in Entry Price", key)
            continue
        today_pnl += value["total_quantity"] * (
            value["exit_price"] - value["entry_price"]
        ) - (value["stt"] + value["brokerage"] + value["others"])
        today_pnl = round(today_pnl, 3)
    return today_pnl


def get_today_pnl_krx(
    dead: dict,
    diagnose: bool = False,
    diagnose_entry_exit_price_diff_limit: int = 100,
) -> float:
    """Function to get today's pnl for krx.

    Args:
        dead (dict): dead order dict
        diagnose (bool, optional): specifies if we want temporary diagnosis. Defaults to False.
        diagnose_entry_exit_price_diff_limit (int, optional): threshold difference. Defaults to 100.

    Returns:
        float: today's pnl
    """

    def tmp_diagnose(key: str, value: dict):
        """Internal function to perform temporary diagnosis on given and values.

        Args:
            key (str): given key
            value (dict): given value dict (dead order)
        """

        if value["total_quantity"] == 0:
            print("ZeroQuantityTrade ", key)

        if value["entry_price"] == 0 or value["exit_price"] == 0:
            print("EntryOrExitPriceZero ", key)

        if (
            abs(value["entry_price"] - value["exit_price"])
            >= diagnose_entry_exit_price_diff_limit
        ):
            print("Warning..diff in entry/exit more than 50 ", key)

        if value["stt"] <= 0 or value["brokerage"] <= 0 or value["others"] <= 0:
            print("SomeChargesAreZero ", key)

    today_pnl = 0
    for key, value in dead.items():
        if diagnose:
            tmp_diagnose(key, value)

        if np.isnan(value["entry_price"]):
            print("Encountered NaN in Entry Price", key)
            continue
        today_pnl += value["total_quantity"] * (
            value["exit_price"] - value["entry_price"]
        ) - (value["stt"] + value["brokerage"] + value["others"])
        today_pnl = round(today_pnl, 3)
    return today_pnl


def get_running_mtm(live: dict, bhav_fo: pd.DataFrame) -> Tuple[float, float]:
    """Function to calculate running mtm.

    Args:
        live (dict): live dict object
        bhav_fo (pd.DataFrame): bhav copy for futures and options

    Returns:
        Tuple[float, float]: running mtm & net flow
    """
    tmp = pd.DataFrame()
    tmp["combined"] = (
        bhav_fo["SYMBOL"]
        + bhav_fo["EXPIRY_DT"]
        + bhav_fo["STRIKE_PR"].astype("int").map(str)
        + bhav_fo["OPTION_TYP"]
    )
    tmp["combined"] = tmp["combined"].str.strip()
    tmp["close"] = bhav_fo["CLOSE"]
    tmp = tmp.set_index("combined")
    tmp.index.duplicated().any()
    tmp = tmp["close"]
    combined_to_close_fo = tmp.to_dict()
    running_mtm = 0
    net_flow = 0
    for key, value in live.items():
        if value["segment"] == "FUTSTK" or value["segment"] == "FUTIDX":
            tmp = (
                value["symbol"]
                + value["expiry"]
                + str(int(value["strike"]))
                + str(value["type"])
            )
            current_price = combined_to_close_fo[tmp]
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            running_mtm += value["total_quantity"] * (
                current_price - value["weighted_price"]
            )
        elif value["segment"] == "CASH":
            net_flow += value["total_quantity"] * value["weighted_price"] * -1
        elif value["segment"] in config.OPTIONS_UNIVERSE_MAPPING["NSE"]:
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            net_flow += value["total_quantity"] * value["weighted_price"] * -1
    return running_mtm, net_flow


def get_running_mtm_comm(
    live: dict, bhav_comm: pd.DataFrame, multiplier: dict
) -> Tuple[float, float]:
    """Function to calculate running mtm for commodities.

    Args:
        live (dict): live dict object
        bhav_comm (pd.DataFrame): bhav copy for commodities (mcx & ncdex)
        multiplier (dict): lotsize information

    Returns:
        Tuple[float, float]: running mtm & net flow
    """

    tmp = pd.DataFrame()
    tmp["combined"] = bhav_comm["symbol"].str.strip() + bhav_comm["expiry"].str.strip()
    tmp["combined"] = tmp["combined"].str.strip()
    tmp["close"] = bhav_comm["close"]
    tmp = tmp.set_index("combined")
    tmp.index.duplicated().any()
    tmp = tmp["close"]
    combined_to_close_fo = tmp.to_dict()

    running_mtm = 0
    net_flow = 0

    for key, value in live.items():
        if value["segment"] == "FUTCOM":
            tmp = value["symbol"] + value["expiry"]
            current_price = combined_to_close_fo[tmp]
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            running_mtm += (
                value["total_quantity"]
                * (current_price - value["weighted_price"])
                * multiplier[value["symbol"]]
            )
        elif value["segment"] == "OPTCOM":
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            net_flow += (
                -1
                * value["total_quantity"]
                * value["weighted_price"]
                * multiplier[value["symbol"]]
            )

    return running_mtm, net_flow


def get_running_mtm_gift(
    live: dict, bhav_comm: pd.DataFrame, multiplier: dict
) -> Tuple[float, float]:
    """Function to calculate running mtm for gift segment.

    Args:
        live (dict): live dict object
        bhav_comm (pd.DataFrame): bhav copy for commodities (mcx & ncdex)
        multiplier (dict): lotsize information

    Returns:
        Tuple[float, float]: running mtm & net flow
    """

    tmp = pd.DataFrame()
    tmp["combined"] = (
        bhav_comm["symbol"].str.strip()
        + pd.to_datetime(bhav_comm["expiry"]).dt.strftime("%d-%b-%Y").str.strip()
    )
    tmp["combined"] = tmp["combined"].str.strip()
    tmp["combined"] = "GIFT_" + tmp["combined"]
    tmp["close"] = bhav_comm["close"]
    tmp = tmp.set_index("combined")
    tmp.index.duplicated().any()
    tmp = tmp["close"]
    combined_to_close_fo = tmp.to_dict()

    running_mtm = 0
    net_flow = 0

    for key, value in live.items():
        if value["segment"] in ["FUTSTK_GIFT", "FUTIDX_GIFT"]:
            tmp = value["symbol"] + value["expiry"]
            current_price = combined_to_close_fo[tmp]
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            running_mtm += value["total_quantity"] * (
                current_price - value["weighted_price"]
            )
        elif value["segment"] == "OPTIDX_GIFT":
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            net_flow += value["total_quantity"] * value["weighted_price"] * -1
    return running_mtm, net_flow


def get_running_mtm_krx(live: dict, df_bhav: pd.DataFrame) -> Tuple[float, float]:
    """Function to get the running mtm for currency.

    Args:
        live (dict): live orders dict
        df_bhav (pd.DataFrame): bhav copy df

    Returns:
        Tuple[float, float]: running mtm, net flow
    """

    running_mtm = 0
    net_flow = 0
    for key, value in live.items():
        if value["segment"] == "FUTIDX_KRX":
            current_price = df_bhav["Close"]
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            running_mtm += value["total_quantity"] * (
                current_price - value["weighted_price"]
            )
        elif value["segment"] == "OPTIDX_KRX":
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            net_flow += value["total_quantity"] * value["weighted_price"] * -1
    return running_mtm, net_flow


def get_running_mtm_us(live: dict, df_bhav: pd.DataFrame) -> Tuple[float, float]:
    """Function to get the running mtm for currency.

    Args:
        live (dict): live orders dict
        df_bhav (pd.DataFrame): bhav copy df

    Returns:
        Tuple[float, float]: running mtm, net flow
    """

    running_mtm = 0
    net_flow = 0
    for key, value in live.items():
        if value["segment"] == "FUTIDX_US":
            current_price = df_bhav.loc[df_bhav.contract == value["symbol"], "Close"]
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            running_mtm += (
                list(
                    value["total_quantity"] * (current_price - value["weighted_price"])
                )
            )[0]
        elif value["segment"] == "OPTIDX_US":
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            net_flow += value["total_quantity"] * value["weighted_price"] * -1
    return running_mtm, net_flow


def get_running_mtm_cur(live: dict, df_bhav: pd.DataFrame) -> Tuple[float, float]:
    """Function to get the running mtm for currency.

    Args:
        live (dict): live orders dict
        df_bhav (pd.DataFrame): _description_

    Returns:
        Tuple[float, float]: _description_
    """
    df_bhav["symbol"] = df_bhav["CONTRACT_D"].str[6:]
    df_bhav = df_bhav.set_index("symbol")
    df_bhav = df_bhav["CLOSE_PRIC"]
    tmp_cur_fo = df_bhav.to_dict()

    running_mtm = net_flow = 0
    for key, value in live.items():
        if value["segment"] == "FUTCUR" or value["segment"] == "OPTCUR":
            if np.isnan(value["weighted_price"]):
                print("NaN entry price ", key)
                continue
            if value["segment"] == "FUTCUR":
                tmp = value["symbol"] + value["expiry"].upper()
                current_price = tmp_cur_fo[tmp]
                running_mtm += (
                    1000
                    * value["total_quantity"]
                    * (current_price - value["weighted_price"])
                )
            if value["segment"] == "OPTCUR":
                net_flow += -1000 * value["total_quantity"] * value["weighted_price"]

    return running_mtm, net_flow


def create_pos_tally_error_df(
    df_sk: pd.DataFrame, df_closeprice: pd.DataFrame
) -> pd.DataFrame:
    """Function to crate position tally mismatch as error df.

    Args:
        df_sk (pd.DataFrame): sk df
        df_closeprice (pd.DataFrame): closeprice df

    Returns:
        pd.DataFrame: error df
    """

    start_error_id = round(10 * (datetime.datetime.now().timestamp()))

    symbols = df_sk["symbol"]

    stock_symbol = symbols.str.extract("(\D+)")[0]
    expiry_cepe = symbols.str.extract("(\d+-\D+-\d+\S+)")[0]
    expiry = expiry_cepe.str.extract("(\d+-\D+-\d+)")[0]
    pece = expiry_cepe.str.extract("([PC][E]\d+\Z)")[0]
    callput = pece.str.extract("([PC][E])")[0]
    strike = pece.str.extract("(\d+\Z)")[0]

    timestamp = [
        datetime.datetime.today().strftime("%Y-%m-%d") + " 15:25:00.101"
    ] * len(symbols)
    timestamp = [
        datetime.datetime.strptime(ts, "%Y-%m-%d %H:%M:%S.%f") for ts in timestamp
    ]
    stock_symbol = list(stock_symbol)
    expiry = ["XX" if i is np.nan else i for i in expiry]
    callput = ["XX" if i is np.nan else i for i in callput]
    strike = ["0" if i is np.nan else i for i in strike]
    total_quantity = list(df_sk.iloc[:, 2] - df_sk.iloc[:, 1])
    logitid = list(range(start_error_id, start_error_id + len(stock_symbol)))
    segment = ["XX"] * len(symbols)
    weighted_price = ["XX"] * len(symbols)
    strategy = ["error"] * len(symbols)

    i = 0
    while i < len(stock_symbol):
        weighted_price[i] = df_closeprice[
            df_closeprice.symbol == symbols[i]
        ].close.iloc[-1]
        if (np.isnan(weighted_price[i])) | (weighted_price[i] == 0):
            print("Detect Zero or NaN values : {}".format(logitid[i]))
        if expiry[i] == "XX":
            segment[i] = "CASH"
        elif callput[i] == "XX":
            if stock_symbol[i] in config.OPTIDX_SYMBOL_LIST:
                segment[i] = "FUTIDX"
            else:
                segment[i] = "FUTSTK"
        else:
            if stock_symbol[i] in config.OPTIDX_SYMBOL_LIST:
                segment[i] = (
                    "OPTIDX"
                    if stock_symbol[i] not in ["SENSEX", "BANKEX"]
                    else "OPTIDX_BSE"
                )
            else:
                segment[i] = "OPTSTK"

        i += 1

    df_error = pd.DataFrame(
        {
            "timestamp": timestamp,
            "segment": segment,
            "symbol": stock_symbol,
            "expiry": expiry,
            "total_quantity": total_quantity,
            "strategy": strategy,
            "weighted_price": weighted_price,
            "type": callput,
            "strike": strike,
        },
        index=logitid,
    )
    return df_error


def create_matlab_tally_pending_df(
    df_logger: pd.DataFrame, df_matlab: pd.DataFrame
) -> pd.DataFrame:
    """Function to create matlab mismatch tally as pending orders.

    Args:
        df_logger (pd.DataFrame): logger df
        df_matlab (pd.DataFrame): matlab df

    Returns:
        pd.DataFrame: pending df
    """

    row1 = len(df_logger)
    row2 = len(df_matlab)
    if row2 > 0:
        df_matlab.columns = [
            "timestamp",
            "strategy",
            "segment",
            "symbol",
            "expiry",
            "type",
            "strike",
            "total_quantity",
            "weighted_price",
        ]
        # df_matlab['expiry'] = df_matlab['expiry'].datetime.strftime('%d-%b-%Y')
        df_matlab["expiry"] = df_matlab["expiry"].apply(
            lambda x: datetime.datetime.strftime(x, "%d-%b-%Y")
        )
        df_matlab.loc[df_matlab["segment"] == "CASH", "expiry"] = "XX"

    key_list = [
        "timestamp",
        "segment",
        "symbol",
        "expiry",
        "total_quantity",
        "strategy",
        "weighted_price",
        "type",
        "strike",
        "executed_quantity",
    ]
    dict_pending = {key: [] for key in key_list}
    logit_id = []

    i = 0
    while i < (row1 + row2):
        dict_pending["timestamp"].append(
            datetime.datetime.strptime(
                datetime.datetime.today().strftime("%Y-%m-%d") + " 15:25:00.101",
                "%Y-%m-%d %H:%M:%S.%f",
            )
        )
        dict_pending["weighted_price"].append(0.0)
        dict_pending["executed_quantity"].append(0)
        if i < row1:
            logit_id.append(int("10010" + str(df_logger.index[i])))
            dict_pending["segment"].append(df_logger["segment"].iloc[i])
            dict_pending["symbol"].append(df_logger["symbol"].iloc[i])
            dict_pending["expiry"].append(df_logger["expiry"].iloc[i])
            dict_pending["total_quantity"].append(
                -1 * df_logger["total_quantity"].iloc[i]
            )
            dict_pending["strategy"].append(df_logger["strategy"].iloc[i])
            dict_pending["type"].append(df_logger["type"].iloc[i])
            dict_pending["strike"].append(df_logger["strike"].iloc[i])

        else:
            logit_id.append(int("10011" + str(df_matlab.index[i - row1])))
            dict_pending["segment"].append(df_matlab["segment"].iloc[i - row1])
            dict_pending["symbol"].append(df_matlab["symbol"].iloc[i - row1])
            dict_pending["expiry"].append(df_matlab["expiry"].iloc[i - row1])
            dict_pending["total_quantity"].append(
                df_matlab["total_quantity"].iloc[i - row1]
            )
            dict_pending["strategy"].append(df_matlab["strategy"].iloc[i - row1])
            dict_pending["type"].append(df_matlab["type"].iloc[i - row1])
            dict_pending["strike"].append(df_matlab["strike"].iloc[i - row1])
        i += 1

    df_pending_temp = pd.DataFrame(dict_pending, index=logit_id)
    return df_pending_temp


def set_exposure(df: pd.DataFrame, mode: str) -> pd.DataFrame:
    """Function to set exposure of the passed df.

    Args:
        df (pd.DataFrame): live or dead sheet df
        mode (str): exchange for which logit is running

    Returns:
        df (pd.DataFrame): df after calculating exposure
    """
    call_short_idx = df[
        (df.segment.isin(config.OPTIONS_UNIVERSE_MAPPING[mode]))
        & (df.total_quantity < 0)
        & (df.type == "CE")
    ].index.to_list()
    put_short_idx = df[
        (df.segment.isin(config.OPTIONS_UNIVERSE_MAPPING[mode]))
        & (df.total_quantity < 0)
        & (df.type == "PE")
    ].index.to_list()
    call_buy_idx = df[
        (df.segment.isin(config.OPTIONS_UNIVERSE_MAPPING[mode]))
        & (df.total_quantity > 0)
        & (df.type == "CE")
    ].index.to_list()
    put_buy_idx = df[
        (df.segment.isin(config.OPTIONS_UNIVERSE_MAPPING[mode]))
        & (df.total_quantity > 0)
        & (df.type == "PE")
    ].index.to_list()
    curr_idx = df[df.segment == "FUTCUR"].index.to_list()
    opt_curr_idx = df[df.segment == "OPTCUR"].index.to_list()
    krx_fut_idx = df[df.segment == "FUTIDX_KRX"].index.to_list()
    krx_opt_idx = df[df.segment == "OPTIDX_KRX"].index.to_list()
    df["exposure"] = df.total_quantity * df.entry_price
    df.loc[put_short_idx, "exposure"] = (
        -1 * df.loc[put_short_idx, "total_quantity"] * df.loc[put_short_idx, "strike"]
    )
    df.loc[call_short_idx, "exposure"] = (
        df.loc[call_short_idx, "total_quantity"] * df.loc[call_short_idx, "strike"]
    )
    df.loc[put_buy_idx, "exposure"] = (
        -1 * df.loc[put_buy_idx, "total_quantity"] * df.loc[put_buy_idx, "strike"]
    )
    df.loc[call_buy_idx, "exposure"] = (
        df.loc[call_buy_idx, "total_quantity"] * df.loc[call_buy_idx, "strike"]
    )
    df.loc[curr_idx, "exposure"] = (
        1000 * df.loc[curr_idx, "total_quantity"] * df.loc[curr_idx, "entry_price"]
    )
    df.loc[opt_curr_idx, "exposure"] = (
        1000
        * df.loc[opt_curr_idx, "total_quantity"]
        * df.loc[opt_curr_idx, "entry_price"]
    )
    df.loc[krx_fut_idx, "exposure"] = (
        df.loc[krx_fut_idx, "total_quantity"] * df.loc[krx_fut_idx, "strike"]
    )
    df.loc[krx_opt_idx, "exposure"] = (
        df.loc[krx_opt_idx, "total_quantity"] * df.loc[krx_opt_idx, "strike"]
    )
    return df


def set_strat_variables(
    df: pd.DataFrame,
    df_live: pd.DataFrame,
    df_dead_trade_live: pd.DataFrame,
    strat_table: pd.DataFrame,
) -> pd.DataFrame:
    """Function to set strategy related variables in live & dead sheet.

    Args:
        df (pd.DataFrame): live or dead sheet df
        df_live (pd.DataFrame): live table (balte oms)
        df_dead_trade_live (pd.DataFrame): dead table (balte oms)
        strat_table (pd.DataFrame): strategy table (curr_info)

    Returns:
        df (pd.DataFrame): df after addition of necessary columns
    """
    # We need dead trade live table to extract CASH order info.
    df_live.columns = ["slave_strat", "logit_id"]
    df_dead_trade_live.columns = ["slave_strat", "logit_id"]
    df_dead_trade_live = pd.concat([df_dead_trade_live, df_live], ignore_index=True)

    df_cash = df.loc[df.segment == "CASH"]
    df_ncash = df.loc[df.segment != "CASH"]
    df_cash = df_cash.merge(
        df_dead_trade_live, how="left", left_on="logit_id", right_on="logit_id"
    )
    df_ncash = df_ncash.merge(
        df_live, how="left", left_on="logit_id", right_on="logit_id"
    )
    df_ncash.loc[pd.isnull(df_ncash.slave_strat), "slave_strat"] = "default_slave"
    df_cash.loc[pd.isnull(df_cash.slave_strat), "slave_strat"] = "default_slave"

    df = pd.concat([df_ncash, df_cash], ignore_index=True)
    df.loc[df.slave_strat == "default_slave", "slave_strat"] = df.loc[
        df.slave_strat == "default_slave", "strategy"
    ]
    df = df.merge(
        strat_table,
        left_on="slave_strat",
        right_on="strategy_name",
        how="left",
    )
    df.drop(["strategy_name"], axis=1, inplace=True)
    df.loc[pd.isnull(df.developer), "developer"] = "None"
    df.loc[pd.isnull(df.book_long), "book_long"] = "None"
    df.loc[pd.isnull(df.book_short), "book_short"] = "None"
    df.rename(columns={"developer": "slave_dev"}, inplace=True)
    return df


def set_pnl(df: pd.DataFrame) -> pd.DataFrame:
    """Function to find the profit or loss while making the dead sheet.

    Args:
        df (pd.DataFrame): dead sheet df

    Returns:
        df (pd.DataFrame): df after addition of PnL column
    """
    df["PnL"] = (
        (df.exit_price - df.entry_price) * df.total_quantity
        - df.stt
        - df.brokerage
        - df.others
    )
    curr_idx = df[df.segment == "FUTCUR"].index.to_list()
    df.loc[curr_idx, "PnL"] = (
        (df.loc[curr_idx, "exit_price"] - df.loc[curr_idx, "entry_price"])
        * df.loc[curr_idx, "total_quantity"]
        * 1000
        - df.loc[curr_idx, "stt"]
        - df.loc[curr_idx, "brokerage"]
        - df.loc[curr_idx, "others"]
    )
    curr_opt_idx = df[df.segment == "OPTCUR"].index.to_list()
    df.loc[curr_opt_idx, "PnL"] = (
        (df.loc[curr_opt_idx, "exit_price"] - df.loc[curr_opt_idx, "entry_price"])
        * df.loc[curr_opt_idx, "total_quantity"]
        * 1000
        - df.loc[curr_opt_idx, "stt"]
        - df.loc[curr_opt_idx, "brokerage"]
        - df.loc[curr_opt_idx, "others"]
    )
    df["PnL"] = df["PnL"].round(3)
    return df


def trade_sanity_check(pending: dict, trade_id: str, trade_dict: dict) -> bool:
    """Function to check trade sanity by assuring that it is not already present in pending trades.

    Args:
        pending (dict): pending trades dict
        trade_id (str): tradeID
        trade_dict (dict): trade information

    Returns:
        bool: False if tradeID is present in pending, True otherwise
    """

    if int(trade_id) in pending:  # Repeated trade

        if (
            pending[int(trade_id)]["total_quantity"] < 0
            and trade_dict["total_quantity"] < 0
        ) or (
            pending[int(trade_id)]["total_quantity"] > 0
            and trade_dict["total_quantity"] > 0
        ):
            logging.debug("RepeatedTradeCash %s", trade_id)

        else:
            logging.warning("DuplicateTrade %s", trade_id)
        return False

    return True


def dump_to_csv(
    dead: dict,
    live: dict,
    pending: dict,
    buffered: dict,
    LOCATION: str,
    key: bytes = None,
):
    """Function to dump logit object to csv.

    Args:
        dead (dict): dead order dict
        live (dict): live order dict
        pending (dict): pending order dict
        buffered (dict): buffered order dict
        LOCATION (str): location to dump at
        key (bytes, optional): key to be used by Fernet. Defaults to None.
    """
    tmp_df = pd.DataFrame.from_dict(dead, orient="index")
    tmp_df.to_csv(LOCATION + "dead_temp.csv", index_label="logit_id")
    tmp_df = pd.DataFrame.from_dict(live, orient="index")
    tmp_df.to_csv(LOCATION + "live_temp.csv", index_label="logit_id")
    tmp_df = pd.DataFrame.from_dict(pending, orient="index")
    tmp_df.to_csv(LOCATION + "pending_temp.csv", index_label="logit_id")
    tmp_df = pd.DataFrame.from_dict(buffered, orient="index")
    tmp_df.to_csv(LOCATION + "buffered_temp.csv", index_label="logit_id")


def load_from_csv(LOCATION: str) -> Tuple[dict, dict, dict]:
    """Function to load logit object from csv.

    Args:
        LOCATION (str): location from which we load

    Returns:
        Tuple[dict, dict, dict, dict]: a tuple with dead, live, pending & buffered order dict
    """
    dead = pd.DataFrame()
    try:
        dead = pd.read_csv(
            LOCATION + "dead_temp.csv", index_col=0, parse_dates=[1, 10]
        )  # Only strike is conv to Integer
    except Exception as e:
        pass
    live = pd.DataFrame()
    try:
        live = pd.read_csv(LOCATION + "live_temp.csv", index_col=0, parse_dates=[1])
    except Exception as e:
        pass
    pending = pd.DataFrame()
    try:
        pending = pd.read_csv(
            LOCATION + "pending_temp.csv", index_col=0, parse_dates=[1]
        )
    except Exception as e:
        pass
    buffered = pd.DataFrame()
    try:
        buffered = pd.read_csv(
            LOCATION + "buffered_temp.csv", index_col=0, parse_dates=[1]
        )
    except Exception as e:
        pass
    return (
        dead.to_dict("index"),
        live.to_dict("index"),
        pending.to_dict("index"),
        buffered.to_dict("index"),
    )


def load_from_csv_mcx(LOCATION: str) -> Tuple[dict, dict, dict]:
    """Function to load logit object from csv for mcx.

    Args:
        LOCATION (str): location from which we load

    Returns:
        Tuple[dict, dict, dict]: a tuple with dead, live, pending & buffered order dict
    """
    dead = pd.DataFrame()
    try:
        dead = pd.read_csv(
            LOCATION + "comm_dead.csv", index_col=0, parse_dates=[1, 10]
        )  # Only strike is conv to Integer
    except Exception as e:
        pass
    live = pd.DataFrame()
    try:
        live = pd.read_csv(LOCATION + "comm_live.csv", index_col=0, parse_dates=[1])
    except Exception as e:
        pass
    pending = pd.DataFrame()
    try:
        pending = pd.read_csv(
            LOCATION + "comm_pending.csv", index_col=0, parse_dates=[1]
        )
    except Exception as e:
        pass
    buffered = pd.DataFrame()
    try:
        buffered = pd.read_csv(
            LOCATION + "comm_buffered.csv", index_col=0, parse_dates=[1]
        )
    except Exception as e:
        pass
    return (
        dead.to_dict("index"),
        live.to_dict("index"),
        pending.to_dict("index"),
        buffered.to_dict("index"),
    )


def load_from_csv_gift(LOCATION: str) -> Tuple[dict, dict, dict]:
    """Function to load logit object from csv for gift.

    Args:
        LOCATION (str): location from which we load

    Returns:
        Tuple[dict, dict, dict]: a tuple with dead, live, pending & buffered order dict
    """
    dead = pd.DataFrame()
    try:
        dead = pd.read_csv(
            LOCATION + "gift_dead.csv", index_col=0, parse_dates=[1, 10]
        )  # Only strike is conv to Integer
    except Exception as e:
        pass
    live = pd.DataFrame()
    try:
        live = pd.read_csv(LOCATION + "gift_live.csv", index_col=0, parse_dates=[1])
    except Exception as e:
        pass
    pending = pd.DataFrame()
    try:
        pending = pd.read_csv(
            LOCATION + "gift_pending.csv", index_col=0, parse_dates=[1]
        )
    except Exception as e:
        pass
    buffered = pd.DataFrame()
    try:
        buffered = pd.read_csv(
            LOCATION + "gift_buffered.csv", index_col=0, parse_dates=[1]
        )
    except Exception as e:
        pass
    return (
        dead.to_dict("index"),
        live.to_dict("index"),
        pending.to_dict("index"),
        buffered.to_dict("index"),
    )


def load_from_csv_krx(LOCATION: str) -> Tuple[dict, dict, dict]:
    """Function to load logit object from csv for krx.

    Args:
        LOCATION (str): location from which we load

    Returns:
        Tuple[dict, dict, dict]: a tuple with dead, live, pending & buffered order dict
    """
    dead = pd.DataFrame()
    try:
        dead = pd.read_csv(
            LOCATION + "krx_dead.csv", index_col=0, parse_dates=[1, 10]
        )  # Only strike is conv to Integer
    except Exception as e:
        pass
    live = pd.DataFrame()
    try:
        live = pd.read_csv(LOCATION + "krx_live.csv", index_col=0, parse_dates=[1])
    except Exception as e:
        pass
    pending = pd.DataFrame()
    try:
        pending = pd.read_csv(
            LOCATION + "krx_pending.csv", index_col=0, parse_dates=[1]
        )
    except Exception as e:
        pass
    buffered = pd.DataFrame()
    try:
        buffered = pd.read_csv(
            LOCATION + "krx_buffered.csv", index_col=0, parse_dates=[1]
        )
    except Exception as e:
        pass
    return (
        dead.to_dict("index"),
        live.to_dict("index"),
        pending.to_dict("index"),
        buffered.to_dict("index"),
    )


def load_from_csv_us(LOCATION: str) -> Tuple[dict, dict, dict]:
    """Function to load logit object from csv for us.

    Args:
        LOCATION (str): location from which we load

    Returns:
        Tuple[dict, dict, dict]: a tuple with dead, live, pending & buffered order dict
    """
    dead = pd.DataFrame()
    try:
        dead = pd.read_csv(
            LOCATION + "us_dead.csv", index_col=0, parse_dates=[1, 10]
        )  # Only strike is conv to Integer
    except Exception as e:
        pass
    live = pd.DataFrame()
    try:
        live = pd.read_csv(LOCATION + "us_live.csv", index_col=0, parse_dates=[1])
    except Exception as e:
        pass
    pending = pd.DataFrame()
    try:
        pending = pd.read_csv(LOCATION + "us_pending.csv", index_col=0, parse_dates=[1])
    except Exception as e:
        pass
    buffered = pd.DataFrame()
    try:
        buffered = pd.read_csv(
            LOCATION + "us_buffered.csv", index_col=0, parse_dates=[1]
        )
    except Exception as e:
        pass
    return (
        dead.to_dict("index"),
        live.to_dict("index"),
        pending.to_dict("index"),
        buffered.to_dict("index"),
    )


def calc_charges(
    tmp: dict, trade_id: str, lot_size: int, store_sharekhan: dict
) -> Tuple[float, float, float]:
    """Function to calculate charges.

    Args:
        tmp (dict): trade information
        trade_id (str): tradeID
        lot_size (int): lot size information
        store_sharekhan (dict): sharekhan information

    Raises:
        ValueError: if a wrong segment is passed

    Returns:
        Tuple[float, float, float]: stt, brokerage, other charges
    """
    if tmp["entry_price"] is None:
        return 0, 0, 0

    bip = 1e-4
    turnover = (
        abs(int(tmp["total_quantity"])) * (tmp["entry_price"] + tmp["exit_price"]) / 2
    )
    if tmp["total_quantity"] < 0:
        sell_px_vol = abs(int(tmp["total_quantity"])) * tmp["entry_price"]
        buy_px_vol = abs(int(tmp["total_quantity"])) * tmp["exit_price"]
        entry_px_vol = sell_px_vol
    else:
        sell_px_vol = abs(int(tmp["total_quantity"])) * tmp["exit_price"]
        buy_px_vol = abs(int(tmp["total_quantity"])) * tmp["entry_price"]
        entry_px_vol = buy_px_vol

    # STT , Brokerage , Others (GST on Brokerage, ST Transaction Charge + SEBI, Stamp Duty)
    tmp_brok = 1
    if (
        trade_id in store_sharekhan and store_sharekhan[trade_id] == 1
    ):  # Kivisecurity Trade
        tmp_brok = 0
    if tmp["segment"] == "FUTIDX_GIFT":
        num_lots = abs(int(tmp["total_quantity"])) / lot_size
        return 0, 0, (0.0002 + 0.00025) * turnover / 100 + 0.8 * num_lots
    elif tmp["segment"] == "OPTIDX_GIFT":
        num_lots = abs(int(tmp["total_quantity"])) / lot_size
        return 0, 0, (0.0002 + 0.0075) * turnover / 100 + 0.0005 * 2 * turnover
    elif tmp["segment"] == "FUTSTK" or tmp["segment"] == "FUTIDX":
        tmp_stt = 2 * sell_px_vol * bip
        tmp_brok *= 0.5 * turnover * bip
        tmp_ipft = 0.01 * 2 * turnover * bip
        return (
            tmp_stt,
            tmp_brok,
            tmp_brok * 0.18
            + (0.173 + 0.01) * 1.18 * 2 * turnover * bip
            + 0.2 * bip * buy_px_vol
            + tmp_ipft,
        )

    elif tmp["segment"] == "CASH":
        tmp_ipft = 0.01 * 2 * turnover * bip
        if tmp["entry_timestamp"].date() == tmp["exit_timestamp"].date():
            tmp_stt = 2.5 * sell_px_vol * bip
            tmp_brok *= (
                0.5
                * bip
                * max((tmp["entry_price"] + tmp["exit_price"]) / 2, 100)
                * abs(int(tmp["total_quantity"]))
            )
            return (
                tmp_stt,
                tmp_brok,
                tmp_brok * 0.18
                + (0.297 + 0.01) * 1.18 * 2 * turnover * bip
                + 0.3 * bip * buy_px_vol
                + tmp_ipft,
            )
        else:
            tmp_stt = 20 * turnover * bip
            if tmp["entry_price"] > 100:
                tmp_brok *= 0
            else:
                tmp_brok *= 0
            return (
                tmp_stt,
                tmp_brok,
                tmp_brok * 0.18
                + (0.297 + 0.01) * 1.18 * 2 * turnover * bip
                + 1.5 * bip * buy_px_vol
                + tmp_ipft,
            )

    elif tmp["segment"] == "OPTIDX":
        tmp_stt = 10 * sell_px_vol * bip
        tmp_ipft = 0.05 * 2 * turnover * bip
        if tmp["symbol"] == "NIFTY":
            tmp_brok *= 14 / 50.0 * abs(int(tmp["total_quantity"]))
        elif tmp["symbol"] == "FINNIFTY":
            tmp_brok *= 20 / 40.0 * abs(int(tmp["total_quantity"]))
        elif tmp["symbol"] == "MIDCPNIFTY":
            tmp_brok *= 20 / 75.0 * abs(int(tmp["total_quantity"]))
        elif tmp["symbol"] == "SENSEX":
            tmp_brok *= 20 / 10.0 * abs(int(tmp["total_quantity"]))
        elif tmp["symbol"] == "BANKEX":
            tmp_brok *= 20 / 15.0 * abs(int(tmp["total_quantity"]))
        else:
            tmp_brok *= 10 / 25.0 * abs(int(tmp["total_quantity"]))
        return (
            tmp_stt,
            tmp_brok,
            tmp_brok * 0.18
            + (3.503 + 0.01) * 1.18 * 2 * turnover * bip
            + 0.3 * bip * buy_px_vol
            + tmp_ipft,
        )

    elif tmp["segment"] == "OPTIDX_BSE":
        tmp_stt = 10 * sell_px_vol * bip
        tmp_ipft = 0.005 * 2 * turnover * bip
        if tmp["symbol"] == "SENSEX":
            tmp_brok *= 20 / 10.0 * abs(int(tmp["total_quantity"]))
        elif tmp["symbol"] == "BANKEX":
            tmp_brok *= 20 / 15.0 * abs(int(tmp["total_quantity"]))
        else:
            tmp_brok *= 10 / 25.0 * abs(int(tmp["total_quantity"]))
        return (
            tmp_stt,
            tmp_brok,
            tmp_brok * 0.18
            + (3.25 + 0.01) * 1.18 * 2 * turnover * bip
            + 0.3 * bip * buy_px_vol
            + tmp_ipft,
        )

    elif tmp["segment"] == "OPTSTK":
        tmp_stt = 10 * sell_px_vol * bip
        tmp_ipft = 0.05 * 2 * turnover * bip
        tmp_brok *= 20.0 * (abs(int(tmp["total_quantity"])) / lot_size)
        return (
            tmp_stt,
            tmp_brok,
            tmp_brok * 0.18
            + (3.503 + 0.01) * 1.18 * 2 * turnover * bip
            + 0.3 * bip * buy_px_vol
            + tmp_ipft,
        )

    elif tmp["segment"] == "FUTCUR":
        tmp_brok *= 0.5 * turnover * bip  # * 1000
        return (
            0,
            tmp_brok * 1000,
            (
                tmp_brok * 0.18
                + (0.035 + 0.01) * 1.18 * 2 * turnover * bip
                + 0.01 * bip * buy_px_vol
            )
            * 1000,
        )

    elif tmp["segment"] == "OPTCUR":
        brok = tmp_brok * 2 * abs(int(tmp["total_quantity"]))
        tmp_brok *= 100 * turnover * bip * 1000
        return (
            0,
            brok,
            brok * 0.18
            + ((3.11 + 0.01) * 1.18 * 2 * turnover * bip + 0.01 * bip * buy_px_vol)
            * 1000,
        )

    elif tmp["segment"] == "FUTCOM":
        tmp_clear = 0.025
        tmp_stamp = 0.2
        tmp_sebi = 0.01
        if tmp["symbol"] in config.FUTCOM_INSTRUMENTS:
            tmp_transc = 0.6  # charges for ncdex trades
            if tmp["entry_timestamp"].date() == tmp["exit_timestamp"].date():
                tmp_rmf = 0
            else:
                tmp_rmf = 0.5
            if tmp["symbol"] in ["COCUDAKL", "GUARGUM5"]:
                tmp_ctt = 1.25 * sell_px_vol * bip * lot_size
                return (
                    tmp_ctt,
                    0,
                    (
                        (
                            (tmp_transc + tmp_sebi + tmp_clear) * 2 * turnover
                            + tmp_rmf * entry_px_vol
                        )
                        * 1.18
                        + tmp_stamp * buy_px_vol
                    )
                    * bip
                    * lot_size,
                )
            else:
                return (
                    0,
                    0,
                    (
                        (
                            (tmp_transc + tmp_sebi + tmp_clear) * 2 * turnover
                            + tmp_rmf * entry_px_vol
                        )
                        * 1.18
                        + tmp_stamp * buy_px_vol
                    )
                    * bip
                    * lot_size,
                )
        else:
            tmp_transc = 0.21  # charges for mcx trades
            if tmp["symbol"] in ["CARDAMOM"]:
                return (
                    0,
                    0,
                    (
                        (tmp_transc + tmp_sebi + tmp_clear) * 1.18 * 2 * turnover
                        + tmp_stamp * buy_px_vol
                    )
                    * bip
                    * lot_size,
                )
            else:
                tmp_ctt = 1.25 * sell_px_vol * bip * lot_size
                return (
                    tmp_ctt,
                    0,
                    (
                        (tmp_transc + tmp_sebi + tmp_clear) * 1.18 * 2 * turnover
                        + tmp_stamp * buy_px_vol
                    )
                    * bip
                    * lot_size,
                )
    elif tmp["segment"] == "OPTCOM":
        tmp_clear = 0.025
        tmp_stamp = 0.3
        tmp_sebi = 0.01
        tmp_transc = 4.18  # charges for mcx option trades
        tmp_ctt = 6.25 * sell_px_vol * bip * lot_size
        return (
            tmp_ctt,
            0,
            (
                (tmp_transc + tmp_sebi + tmp_clear) * 1.18 * 2 * turnover
                + tmp_stamp * buy_px_vol
            )
            * bip
            * lot_size,
        )
    elif tmp["segment"] in ["OPTIDX_KRX"]:
        entry_brokerage = max(
            1000,
            abs(int(tmp["total_quantity"])) * 0.002 * (tmp["entry_price"]),
        )
        exit_brokerage = max(
            1000, abs(int(tmp["total_quantity"])) * 0.002 * (tmp["exit_price"])
        )
        if pd.Timestamp(tmp["exit_timestamp"]) >= pd.to_datetime(
            tmp["expiry"]
        ) + timedelta(hours=15, minutes=30):
            exit_brokerage = 0
        return 0, entry_brokerage + exit_brokerage, 0
    elif tmp["segment"] in ["FUTIDX_KRX"]:
        entry_brokerage = (
            abs(int(tmp["total_quantity"])) * 0.00004 * (tmp["entry_price"])
        )
        exit_brokerage = abs(int(tmp["total_quantity"])) * 0.00004 * (tmp["exit_price"])
        if pd.Timestamp(tmp["exit_timestamp"]) >= pd.to_datetime(
            tmp["expiry"]
        ) + timedelta(hours=15, minutes=30):
            exit_brokerage = 0
        return 0, entry_brokerage + exit_brokerage, 0
    elif tmp["segment"] in ["FUTIDX_US"]:
        execution_fee = (
            2
            * abs(int(tmp["total_quantity"]) / lot_size)
            * TRANSACTION_CHARGES_US.get(tmp["symbol"], (0.25, 0.35))[0]
        )
        exchange_fee = (
            2
            * abs(int(tmp["total_quantity"]) / lot_size)
            * TRANSACTION_CHARGES_US.get(tmp["symbol"], (0.25, 0.35))[1]
        )
        regulatory_fee = 2 * abs(int(tmp["total_quantity"]) / lot_size) * 0.02

        def calcualte_number_of_days(
            entry_date: pd.Timestamp, exit_date: pd.Timestamp
        ) -> int:
            return config.DATES_TO_INDEX[exit_date] - config.DATES_TO_INDEX[entry_date]

        trade_duration: int = calcualte_number_of_days(
            entry_date=tmp["entry_timestamp"].normalize(),
            exit_date=tmp["exit_timestamp"].normalize(),
        )
        overnight_charges = (
            0.1 * abs(int(tmp["total_quantity"]) / lot_size) * trade_duration
        )
        return 0, execution_fee + exchange_fee + regulatory_fee + overnight_charges, 0
    elif tmp["segment"] in ["OPTIDX_US"]:
        exit_price = tmp["exit_price"]
        entry_price = tmp["entry_price"]
        lots = abs(int(tmp["total_quantity"]) / lot_size)
        exit_brokerage = 0
        if exit_price < 0.05:
            exit_brokerage = 0.25
        elif exit_price < 0.1:
            exit_brokerage = 0.5
        else:
            exit_brokerage = 0.65
        exit_brokerage *= lots
        exit_brokerage = max(exit_brokerage, 1)  # For exit
        if pd.Timestamp(tmp["exit_timestamp"]) >= pd.to_datetime(
            tmp["expiry"]
        ) + timedelta(hours=22, minutes=0):
            exit_brokerage = 0
        entry_brokerage = 0
        if entry_price < 0.05:
            entry_brokerage = 0.25
        elif entry_price < 0.1:
            entry_brokerage = 0.5
        else:
            entry_brokerage = 0.65
        entry_brokerage *= lots
        entry_brokerage = max(entry_brokerage, 1)  # For entry
        brokerage = entry_brokerage + exit_brokerage
        regulatory_fee = 0.02685 * lots * 2  # For entry and exit
        if tmp["total_quantity"] > 0:
            # Take exit notional as value of aggregate sales
            notional = exit_price * abs(tmp["total_quantity"])
        else:
            notional = tmp["entry_price"] * abs(tmp["total_quantity"])
        transaction_fee = 0.0000278 * notional
        trading_activity_fee = 0.00279 * lots
        clearing_fee = 0.02 * lots * 2
        return (
            0,
            brokerage,
            transaction_fee + trading_activity_fee + regulatory_fee + clearing_fee,
        )
    else:
        raise ValueError("Couldn't find a proper segment")


def convert_dead_to_live(trade_dict: dict) -> dict:
    """Function to convert dead trade to live.

    Args:
        trade_dict (dict): dead trade information

    Returns:
        dict: live trade information
    """
    tmp = dict()
    tmp["timestamp"] = trade_dict["exit_timestamp"]
    tmp["segment"] = trade_dict["segment"]
    tmp["symbol"] = trade_dict["symbol"]
    tmp["expiry"] = trade_dict["expiry"]
    tmp["total_quantity"] = trade_dict["total_quantity"]
    tmp["strategy"] = trade_dict["strategy"]
    tmp["weighted_price"] = trade_dict["exit_price"]
    tmp["type"] = trade_dict["type"]
    tmp["strike"] = trade_dict["strike"]
    return tmp


def generate_error_trade_string(new_trade_id: str, trade_dict: dict) -> str:
    """Function to generate error trade string.

    Args:
        new_trade_id (str): new tradeID of error order
        trade_dict (dict): trade dict information

    Returns:
        str: error trade string
    """
    tmp = (
        trade_dict["segment"]
        + ","
        + trade_dict["symbol"]
        + ","
        + trade_dict["expiry"]
        + ","
    )
    tmp += (
        str(trade_dict["type"])
        + ","
        + str(trade_dict["strike"])
        + ","
        + str(int(abs(trade_dict["total_quantity"])))
        + ","
    )
    if trade_dict["total_quantity"] > 0:  # Opposite
        if trade_dict["segment"] == "CASH":
            tmp += "SS" + ","
        else:
            tmp += "S" + ","
    elif trade_dict["total_quantity"] < 0:
        tmp += "B" + ","
    else:
        tmp = "Why you sending 0 quantity error trade ?..remove it from live"
        return tmp
    tmp += "10010" + str(new_trade_id) + ","  # error trades always exit
    # tmp += "0.00,0.00,0.00,0.00,error"
    tmp += "0.0,0.0,0.0,0.00,,,,,error"

    if trade_dict["segment"] == "FUTCOM":
        tmp = dt.datetime.now().strftime("%d-%m-%Y,%H:%M:%S.00") + "," + tmp

    return tmp


def get_engine(config_str: str) -> sa.engine:
    """Function to create sqlalchemy engine.

    Args:
        config_str (str): config for creating the engine

    Returns:
        sa.engine: sqlalachemy engine
    """
    return sa.create_engine(config_str)


def minio_file_downloader(bucket_name: str, source_name: str, target_name: str):
    """Function to download file from minio.

    Args:
        bucket_name (str): bucket name
        source_name (str): source name
        target_name (str): target name
    """
    config.minioClient.fget_object(bucket_name, source_name, target_name)


def get_contract_name(
    segment: str, symbol: str, expiry: str, opt_type: str, strike: int
) -> str:
    """Function to get contract name.

    Args:
        segment (str): segment name
        symbol (str): symbol name
        expiry (str): expiry
        opt_type (str): option type
        strike (int): strike price

    Returns:
        str: contract name
    """
    if segment in ["CASH"]:
        return symbol
    elif segment in config.OPTIONS_UNIVERSE:
        return symbol + expiry + opt_type + str(strike)
    else:
        return symbol + expiry


def previous_date(
    ALL_DATES: list, date: pd.Timestamp, lookback: int = 1
) -> pd.Timestamp:
    """
    Lookback from current date.

    Args:
        date (pandas.Timestamp): Date from which lookback is required
        lookback (int): Number of days to lookback

    Returns:
        date (pandas.Timestamp): Previous date after lookback
    """
    index = np.where(ALL_DATES >= date)
    if len(index[0]) == 0:
        raise IndexError("%s might be a holiday date...try a nearby date" % date)
    if index[0][0] - lookback < 0:
        return ALL_DATES[0]
    return ALL_DATES[index[0][0] - lookback]


def upload_df_to_minio(bucket_name: str, filename: str, df: pd.DataFrame):
    """Function to upload df to minio.

    Args:
        bucket_name (str): name of the minio bucket
        filename (str): given filename
        df (pd.DataFrame): df to be uploaded
    """
    config.minioClient.put_object(
        bucket_name, filename, BytesIO(df.to_parquet()), len(df.to_parquet())
    )


def fetch_df_from_minio(
    bucket_name: str,
    filename: str,
    parquet_mode: bool = True,
    international_exchanges: bool = False,
) -> pd.DataFrame:
    """Function to fetch df from minio.

    Args:
        bucket_name (str): name of minio bucket
        filename (str): given filename to be fetched

    Returns:
        pd.DataFrame: fetched df
    """
    minio_client = (
        config.minioClientInternationalExchanges
        if international_exchanges
        else config.minioClient
    )
    if parquet_mode:
        df = pd.read_parquet(
            BytesIO(minio_client.get_object(bucket_name, filename).data)
        )
    else:
        df = pd.read_csv(BytesIO(minio_client.get_object(bucket_name, filename).data))
    return df


from logit.data_utility import get_ltp
from sqlalchemy import text


def live_sheet(obj):
    """Function to process and dump live sheet to sql table.

    Args:
        obj (Logit): logit object
    """
    import time

    segment_list = get_todays_segment_list(pd.Timestamp.now().normalize(), obj.mode)
    live_sheet_name: str = f"live_sheet_{obj.mode.lower()}"
    curr_ltp = pd.DataFrame()
    for segment in segment_list:
        if obj.mode == "KRX":
            curr_ltp = get_ltp(
                segment=segment,
                hour1=pd.Timestamp.now(tz="Asia/Seoul").hour,
                minute1=pd.Timestamp.now(tz="Asia/Seoul").minute,
            )
        elif obj.mode == "US":
            current_time = pd.Timestamp.now(tz=obj.timezone).replace(
                tzinfo=None
            ) - pd.Timedelta(minutes=1)
            curr_ltp = get_ltp(
                segment=segment,
                hour1=current_time.hour,
                minute1=current_time.minute,
                logit_date=obj.logit_date,
            )
        else:
            curr_ltp = get_ltp(
                segment=segment,
                hour1=pd.Timestamp.now().hour,
                minute1=pd.Timestamp.now().minute,
            )
        obj.ltp = pd.concat([obj.ltp, curr_ltp])
        obj.ltp.drop_duplicates(subset="contract", keep="last", inplace=True)
    start = time.time()
    df = copy.deepcopy(obj.df_live)
    if len(df) == 0:
        display("Live sheet is empty so skipping and clearing live sheet")
        sql_query = text(f"DELETE FROM {live_sheet_name}")
        with obj.portfolio_stats_engine.connect() as connection:
            connection.execute(sql_query)
        return
    if obj.mode in ["COMM"]:
        df["total_quantity"] = df.apply(
            lambda row: row["total_quantity"] * obj.store_lot_size[row["symbol"]],
            axis=1,
        )
    df = df.rename_axis("logit_id").reset_index()
    df["strike"] = df["strike"].astype(str)
    df["contract"] = df["symbol"]
    df.loc[(df.segment.isin(config.FUTURES_UNIVERSE)), "contract"] = (
        df["symbol"] + df["expiry"]
    )
    df["strike_temp"] = df["strike"].astype(str)
    df["strike_temp"] = df["strike_temp"].str.rstrip("0").str.rstrip(".")
    df.loc[(df.segment.isin(config.OPTIONS_UNIVERSE)), "contract"] = (
        df["symbol"] + df["expiry"] + df["type"] + df["strike_temp"]
    )
    df.loc[(df.segment == "OPTCUR"), "contract"] = (
        df["symbol"] + df["expiry"] + df["type"] + df["strike"].astype(str)
    )
    df.loc[((df.segment == "FUTIDX_KRX"), "contract")] = (
        df["symbol"] + "_" + df["expiry"]
    )
    df.loc[((df.segment == "OPTIDX_KRX"), "strike")] = pd.to_numeric(
        df["strike"].str.replace(".", "", regex=False), errors="coerce"
    )
    df.loc[df.segment == "OPTIDX_KRX", "contract"] = (
        df["symbol"]
        + "_"
        + df["expiry"]
        + "_"
        + df["type"]
        + "_"
        + df["strike"].astype(str)
    )
    try:
        df = df.merge(obj.ltp, how="left", left_on="contract", right_on="contract")
        df.loc[
            (df.segment.isin(config.FUTURES_UNIVERSE)) & (df.ltp.isna()), "contract"
        ] = (df["symbol"] + df["expiry"].str.replace("-", "").str.upper())
        df = check_remove_column(df=df, col_names=["index", "ltp", "cons_volume"])
        df = df.merge(obj.ltp, how="left", left_on="contract", right_on="contract")
        df = check_remove_column(df=df, col_names=["index"])
    except Exception as e:
        print("ltp was not updated. Dumping live sheet")
        df["ltp"] = np.nan
    df = check_remove_column(
        df=df, col_names=["strike_temp", "contract", "cons_volume"]
    )
    df.rename(
        columns={
            "weighted_price": "entry_price",
            "timestamp": "entry_timestamp",
        },
        inplace=True,
    )
    df = df.astype(object).where(pd.notnull(df), None)
    df.loc[df["expiry"] == "XX", "expiry"] = "2100-01-01"
    df["entry_timestamp"] = pd.to_datetime(df["entry_timestamp"])
    df["expiry"] = pd.to_datetime(df["expiry"]).dt.date
    df["entry_price"] = df["entry_price"].astype(np.double)
    df["total_quantity"] = df["total_quantity"].astype(np.double)
    df["strike"] = df["strike"].astype(np.double)
    df["logit_id"] = df["logit_id"].astype(np.int64)
    df["exchange"] = obj.mode
    df = set_exposure(df=df, mode=obj.mode)

    df_dead_trade_live: pd.DataFrame = pd.DataFrame(columns=["slave_strat", "logit_id"])
    if obj.mode in ["NSE"]:
        df_live = pd.read_sql(
            f"select SLAVE_NAME, TRADEID from {config.BALTE_LIVE_TABLE_NSE}",
            con=obj.nse_oms_engine,
        )
        df_dead_trade_live = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_NSE} where SEGMENT = "CASH"',
            con=obj.nse_oms_engine,
        )
        if len(df_dead_trade_live) == 0:
            df_dead_trade_live = pd.read_sql(
                f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_20_DAYS_NSE} where SEGMENT = "CASH"',
                con=obj.nse_oms_engine,
            )
    elif obj.mode in ["KRX"]:
        df_live = pd.read_sql(
            f"select SLAVE_NAME, TRADEID from {config.BALTE_LIVE_TABLE_KRX}",
            con=obj.centralised_oms_engine,
        )
    elif obj.mode in ["GIFT"]:
        df_live = pd.read_sql(
            f"select SLAVE_NAME, TRADEID from {config.BALTE_LIVE_TABLE_GIFT}",
            con=obj.centralised_oms_engine,
        )
    elif obj.mode in ["US"]:
        df_live = pd.read_sql(
            f"select SLAVE_NAME, TRADEID from {config.BALTE_LIVE_TABLE_US}",
            con=obj.centralised_oms_engine,
        )
    elif obj.mode in ["MCX", "COMM"]:
        df_live = pd.read_sql(
            f"select SLAVE_NAME, TRADEID from {config.BALTE_LIVE_TABLE_MCX}",
            con=obj.centralised_oms_engine,
        )

    curr_info = pd.read_sql(
        f"select strategy_name, developer, book_long, book_short from {config.STRAT_TABLE}",
        con=obj.strat_db_engine,
    )
    df = set_strat_variables(
        df=df,
        df_live=df_live,
        df_dead_trade_live=df_dead_trade_live,
        strat_table=curr_info,
    )
    sql_query = text(f"DELETE FROM {live_sheet_name}")
    with obj.portfolio_stats_engine.connect() as connection:
        connection.execute(sql_query)
    df.loc[df.segment == "FUTCUR", "total_quantity"] *= 1000
    df["PNL"] = (df.ltp - df.entry_price) * df.total_quantity
    df.loc[df.segment == "OPTIDX_KRX", "strike"] /= 10
    df.loc[df.segment == "FUTIDX_KRX", "strike"] /= 10
    df["PNL"] = df["PNL"].astype(np.double)
    df["PNL"] = df["PNL"].round(3)
    df.loc[df.segment == "FUTCUR", "total_quantity"] /= 1000

    try:
        df.to_sql(
            name=live_sheet_name,
            con=obj.portfolio_stats_engine,
            index=False,
            if_exists="append",
        )
    except Exception as e:
        print(f"Skipping dumping live sheet to timescale db due to {repr(e)}")
    display("Live sheet updated!")
    print(f"Took {time.time() - start}s to dump live sheet!")


def dead_sheet(obj, load_full_dead_sheet: bool = False):
    """Function to process and dump dead sheet to sql table.

    Args:
        obj (Logit): logit object
        load_full_dead_sheet (bool): Flag to indicate if we want to load the full dead sheet.
    """
    import time

    start = time.time()
    df = copy.deepcopy(obj.df_dead)
    if obj.mode in ["COMM"]:
        df["total_quantity"] = df.apply(
            lambda row: row["total_quantity"] * obj.store_lot_size[row["symbol"]],
            axis=1,
        )
    if len(df) == 0:
        display("Dead sheet is empty so skipping")
        return
    df.loc[df["expiry"] == "XX", "expiry"] = "2127-10-07"
    df = df.rename_axis("logit_id").reset_index()
    df["total_quantity"] = df["total_quantity"].astype(np.double)
    df["entry_timestamp"] = pd.to_datetime(df["entry_timestamp"])
    df["entry_date"] = df["entry_timestamp"].dt.normalize()
    df["exit_timestamp"] = pd.to_datetime(df["exit_timestamp"])
    df["exit_date"] = df["exit_timestamp"].dt.normalize()
    df["entry_price"] = df["entry_price"].astype(np.double)
    df["exit_price"] = df["exit_price"].astype(np.double)
    df["brokerage"] = df["brokerage"].astype(np.double)
    df["stt"] = df["stt"].astype(np.double)
    df["others"] = df["others"].astype(np.double)
    df["strike"] = df["strike"].astype(np.double)
    df["logit_id"] = df["logit_id"].astype(np.int64)
    df["expiry"] = pd.to_datetime(df["expiry"]).dt.date
    df["exchange"] = obj.mode
    df = set_exposure(df=df, mode=obj.mode)

    df = set_pnl(df=df)

    df_merged_dead_trade_live: pd.DataFrame = pd.DataFrame(
        columns=["slave_strat", "logit_id"]
    )
    if obj.mode in ["NSE"]:
        df_dead = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_NSE} where SEGMENT != "CASH"',
            con=obj.nse_oms_engine,
        )
        if (len(df_dead) == 0) or (load_full_dead_sheet):
            df_dead = pd.read_sql(
                f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_20_DAYS_NSE} where SEGMENT != "CASH"',
                con=obj.nse_oms_engine,
            )
        df_live = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.BALTE_LIVE_TABLE_NSE} WHERE SEGMENT != "CASH"',
            con=obj.nse_oms_engine,
        )
        df_merged_dead = merge_df(df1=df_dead, df2=df_live)
        df_dead_trade_live = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_NSE} where SEGMENT = "CASH"',
            con=obj.nse_oms_engine,
        )
        if (len(df_dead_trade_live) == 0) or (load_full_dead_sheet):
            df_dead_trade_live = pd.read_sql(
                f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_20_DAYS_NSE} where SEGMENT = "CASH"',
                con=obj.nse_oms_engine,
            )
        df_live = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.BALTE_LIVE_TABLE_NSE} WHERE SEGMENT = "CASH"',
            con=obj.nse_oms_engine,
        )
        df_merged_dead_trade_live = merge_df(df_dead_trade_live, df_live)
    elif obj.mode in ["KRX"]:
        df_dead = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_20_DAYS_KRX} where entry_timestamp > "2024-01-01"',
            con=obj.centralised_oms_engine,
        )
        df_dead_total = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_TOTAL_KRX} where entry_timestamp > "2024-01-01"',
            con=obj.centralised_oms_engine,
        )
        df_merged_dead = pd.concat([df_dead, df_dead_total])
    elif obj.mode in ["US"]:
        df_dead = pd.read_sql(
            f"select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_20_DAYS_US}",
            con=obj.centralised_oms_engine,
        )
        df_dead_total = pd.read_sql(
            f"select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_TOTAL_US}",
            con=obj.centralised_oms_engine,
        )
        df_merged_dead = pd.concat([df_dead, df_dead_total])
    elif obj.mode in ["GIFT"]:
        df_dead = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_20_DAYS_GIFT} where entry_timestamp > "2024-01-01"',
            con=obj.centralised_oms_engine,
        )
        df_dead_total = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_TOTAL_GIFT} where entry_timestamp > "2024-01-01"',
            con=obj.centralised_oms_engine,
        )
        df_merged_dead = pd.concat([df_dead, df_dead_total])
    elif obj.mode in ["MCX", "COMM"]:
        df_dead = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_LIVE_20_DAYS_MCX} where entry_timestamp > "2024-01-01"',
            con=obj.centralised_oms_engine,
        )
        df_dead_total = pd.read_sql(
            f'select SLAVE_NAME, TRADEID from {config.DEAD_TRADE_TOTAL_MCX} where entry_timestamp > "2024-01-01"',
            con=obj.centralised_oms_engine,
        )
        df_merged_dead = pd.concat([df_dead, df_dead_total])
    curr_info = pd.read_sql(
        f"select strategy_name, developer, book_long, book_short from {config.STRAT_TABLE}",
        con=obj.strat_db_engine,
    )
    df = set_strat_variables(
        df=df,
        df_live=df_merged_dead,
        df_dead_trade_live=df_merged_dead_trade_live,
        strat_table=curr_info,
    )
    len_slave_cluster_mapping_error: int = len(
        df[(df.strategy == df.slave_strat) & (df.slave_strat.str.contains("cluster"))]
    )
    if len_slave_cluster_mapping_error:
        if load_full_dead_sheet:
            display(
                f"Cluster slave mapping looks off even after loading full dead sheet! Found {len_slave_cluster_mapping_error} mapping issues. Please check!"
            )
        else:
            display("Loading full dead sheet before dumping!")
            dead_sheet(obj=obj, load_full_dead_sheet=True)
            return
    table_name: str = f"dead_sheet_{obj.mode.lower()}"
    data_dict = {}
    if obj.mode == "GIFT":
        working_time_start = pd.Timestamp(obj.logit_date).replace(hour=16, minute=0)
        working_time_end = previous_date(
            ALL_DATES=config.ALL_DATES_GIFT, date=obj.logit_date, lookback=-1
        ).replace(hour=16, minute=0)
        data_dict["start_time"] = str(working_time_start)
        data_dict["end_time"] = str(working_time_end)
    else:
        data_dict["start_time"] = str(obj.logit_date)
        data_dict["end_time"] = str((obj.logit_date + pd.Timedelta(days=2)))
    sql_query = text(
        f"DELETE FROM {table_name} WHERE exit_timestamp BETWEEN :start_time AND :end_time"
    )
    with obj.portfolio_stats_engine.connect() as connection:
        connection.execute(sql_query, **data_dict)
    try:
        df.to_sql(
            name=table_name,
            con=obj.portfolio_stats_engine,
            index=False,
            if_exists="append",
        )
    except Exception as e:
        print(f"Error dumping dead sheet to timescale db {e}")
    display("Dead sheet updated!")
    print(f"Took {time.time() - start}s to dump dead sheet!")


def merge_df(df1: pd.DataFrame, df2: pd.DataFrame) -> pd.DataFrame:
    """Function to merge dead sheet and live sheet SLAVE_NAME and TRADEIDs.

    Args:
        df1 (pd.DataFrame): dead sheet
        df2 (pd.DataFrame): live sheet

    Returns:
        pd.DataFrame: merged df
    """
    merged_df = df1.merge(df2, on="TRADEID", how="outer")
    mask = (merged_df["SLAVE_NAME_x"] == "default_slave") | (
        merged_df["SLAVE_NAME_x"].isnull()
    )
    merged_df.loc[mask, "SLAVE_NAME_x"] = merged_df.loc[mask, "SLAVE_NAME_y"]
    merged_df.loc[merged_df.SLAVE_NAME_x.isna(), "SLAVE_NAME_x"] = "default_slave"
    merged_df.drop("SLAVE_NAME_y", axis=1, inplace=True)
    merged_df.rename(columns={"SLAVE_NAME_x": "SLAVE_NAME"}, inplace=True)
    return merged_df


def check_remove_column(df: pd.DataFrame, col_names: List[str]) -> pd.DataFrame:
    """Function to check if column & remove column from dataframe.

    Args:
        df (pd.DataFrame): given df
        col_name (List[str]): column name list

    Returns:
        pd.DataFrame: df after removing column
    """
    for col_name in col_names:
        if col_name in df.columns:
            df.drop(col_name, axis=1, inplace=True)
    return df


def apply_corporate_actions(df_live: pd.DataFrame) -> pd.DataFrame:
    """Applies corporate actions to the live positions at the start of the day.

    Args:
        df_live (pd.DataFrame): live positions

    Returns:
        df_live (pd.DataFrame): live positions after applying corpact
    """
    try:
        print("")
        minio_file_downloader(
            "commondata", "balte_uploads/corpact_today.csv", "./csv/corpacts.csv"
        )
        corpacts = pd.read_csv("./csv/corpacts.csv")
        print(f"found {len(corpacts)} corporate actions")
        corpacts_div = corpacts[corpacts.type == "DIVIDEND"]
        minio_file_downloader(
            "commondata", "nse_daily_downloads/cm_bhav.csv", "./csv/cm_bhav.csv"
        )
        df_bhav = pd.read_csv("./csv/cm_bhav.csv")
        df_bhav = df_bhav[df_bhav.SERIES.isin(["EQ", "BE", "BZ", "BT"])]
        df_bhav = df_bhav[
            [
                "SYMBOL",
                "TIMESTAMP",
                "CLOSE",
            ]
        ]
        df_bhav.columns = [
            "symbol",
            "timestamp",
            "close",
        ]
        for sym, _df in corpacts_div.groupby("symbol"):
            close_price = df_bhav[df_bhav.symbol == sym].close.to_list()[0]
            _df["div_factor"] = 1 - _df["adj_factor"]
            total_adj_factor = _df["div_factor"].sum() * close_price
            print(
                f"applying corpact for symbol {sym} with dividend of {total_adj_factor}"
            )
            len_cash = len(
                df_live.loc[(df_live.segment == "CASH") & (df_live.symbol == sym)]
            )
            print(f"found {len_cash} entries for {sym} in CASH segment")
            df_live.loc[
                (df_live.segment == "CASH") & (df_live.symbol == sym), "weighted_price"
            ] -= total_adj_factor
            if (total_adj_factor / close_price) > 0.02:
                len_fut = len(
                    df_live.loc[(df_live.segment == "FUTSTK") & (df_live.symbol == sym)]
                )
                print(f"found {len_fut} entries for {sym} in FUTSTK segment")
                df_live.loc[
                    (df_live.segment == "FUTSTK") & (df_live.symbol == sym),
                    "weighted_price",
                ] -= total_adj_factor

        corpacts_bonus_split = corpacts[corpacts.type.isin(["SPLIT", "BONUS"])]
        for sym, _df in corpacts_bonus_split.groupby("symbol"):
            total_adj_factor = _df.adj_factor.prod()
            print(
                f"applying corpact for symbol {sym} with adjustment factor of {total_adj_factor}"
            )
            len_cash = len(
                df_live.loc[(df_live.segment == "CASH") & (df_live.symbol == sym)]
            )
            print(f"found {len_cash} entries for {sym} in CASH segment")
            df_live.loc[
                (df_live.segment == "CASH") & (df_live.symbol == sym), "weighted_price"
            ] *= total_adj_factor
            df_live.loc[
                (df_live.segment == "CASH") & (df_live.symbol == sym), "total_quantity"
            ] /= total_adj_factor
            if total_adj_factor < 0.98:
                len_fut = len(
                    df_live.loc[(df_live.segment == "FUTSTK") & (df_live.symbol == sym)]
                )
                print(f"found {len_fut} entries for {sym} in FUTSTK segment")
                df_live.loc[
                    (df_live.segment == "FUTSTK") & (df_live.symbol == sym),
                    "weighted_price",
                ] *= total_adj_factor
                df_live.loc[
                    (df_live.segment == "FUTSTK") & (df_live.symbol == sym),
                    "total_quantity",
                ] /= total_adj_factor
        df_live["total_quantity"] = df_live["total_quantity"].apply(np.floor)
        df_live["total_quantity"] = df_live["total_quantity"].astype(np.int64)
        print("corpact applying done")
        print("")
        return df_live
    except Exception as e:
        print(f"failed to apply corpact due to {e}")


def get_todays_segment_list(date: pd.Timestamp, mode: str) -> List[str]:
    """Function to return the given date's working segemnt list.

    Args:
        date (pd.Timestamp): the given date
        mode (str): mode of logit operation

    Returns:
        List[str]: list of working segments
    """
    segments = config.SEGMENTS[mode]
    segment_list = []
    for segment in segments:
        if segments in ["NF", "CASH", "BSE_NF"] and date not in config.ALL_DATES_NSE:
            continue
        elif segment in ["XN"] and date not in config.ALL_DATES_FX:
            continue
        elif segment in ["MCX"] and date not in config.ALL_DATES_MCX:
            continue
        elif segment in ["NCDEX"] and date not in config.ALL_DATES_NCDEX:
            continue
        elif (
            segment in ["OPTIDX_KRX", "FUTIDX_KRX"] and date not in config.ALL_DATES_KRX
        ):
            continue
        elif segment in ["GIFT"] and date not in config.ALL_DATES_GIFT:
            continue
        else:
            segment_list.append(segment)
    return segment_list


def download_file(url: str, file_name: str):
    """Function to download file from the internet.

    Args:
        url (str): url to download
        file_name (str): file name under which we download
    """
    headers = {
        "User-Agent": "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:1.8.1.6) Gecko/20070802 SeaMonkey/1.1.4"
    }
    response = requests.get(url, stream=True, headers=headers)
    destination_path = os.path.join("./csv/", file_name)
    if response.status_code == 200:
        with open(destination_path, "wb") as file:
            file.write(response.content)
        print(f"File downloaded successfully to {destination_path}")
    else:
        print(f"Failed to download file. Status code: {response.status_code}")
