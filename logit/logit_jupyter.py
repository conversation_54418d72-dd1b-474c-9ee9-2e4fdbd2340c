from logit.main import Logit
from IPython.display import display
import ipywidgets as widgets
from logit.logit_notebook_functions import (
    update_df_view,
    save_df_changes,
    get_ltp_notebook,
    error_order,
    generate_error_string_df,
    generate_error_string_seg_strat,
    exit_orders_manually,
    tally_position_with_matlab,
    add_matlab_trade_to_pending_orders,
    tally_shareakhan_positions,
    add_sharekhan_mismatch_to_live_orders,
    match_cash_positions,
    add_cash_mismatch_to_live_orders,
    reconcile_intraday_cashnet,
    dead_sheet_cashnet,
    exit_options_on_expiry_date,
    apply_corporate_action,
    dump_csv,
    load_csv,
    load_csv_df,
    eod_checks,
    enter_orders_manually,
    live_sheet_processing,
    dead_sheet_processing,
    enter_orders_manually_pending,
    ncdex_pricing,
    add_order_entries_for_buffered_exits_in_pending,
)
import json
import functools
from logit.utility import cstr


class LogitNotebook:
    """Class having all the attributes and methods related to jupyter notebook.

    Attributes:
        obj (Logit): logit object
        layout (widgets.Layout): specifies the layout used by various widgets
        layout_hbox (widgets.Layout): specifies the layout used by HBox widget
        layout_hbox_column (widgets.Layout): specifies the column layout used by HBox widget
        confirmation_widget (widgets.Button): confirmation widget popup
        hbox_confirmation (widgets.HBox): hbox containing confirmation widget
        hour_input (widgets.IntText): hour input for ltp function
        minute_input (widgets.IntText): minute input for ltp function
        hour_input_sk (widgets.IntText): hour input for sharekhan mismatch addition function
        minute_input_sk (widgets.IntText): minute input for sharekhan mismatch addition function
        hour_input_cash (widgets.IntText): hour input for cash mismatch addition function
        minute_input_cash (widgets.IntText): minute input for cash mismatch addition function
        strategy_name_error_input (widgets.Text): strategy name input for error generation
        segment_name_error_input (widgets.Text): segment name input for error generation
        ltp_symbols (widgets.TextArea): symbol list input to overwrite ltp prices
        ltp_prices (widgets.TextArea): price list input to overwrite ltp prices
        symbol_to_exit (widgets.Text): symbol name input to exit options on expiry day
        spot_price (widgets.FloatText): spot price to exit options on expiry day
        symbol_to_apply (widgets.Text): symbol name input to apply corporate action
        adj_factor (widgets.FloatText): adjustmet factor while applying corpact
        error_id_input (widgets.IntText): error id input while building dead sheet from cashnet
        strategy_name_input (widgets.Text): strategy name input while building dead sheet from cashnet
        add_error_trade (widgets.RadioButtons): option to add as error trade
        wid (widgets.RadioButtons): option to select the df view (live, pending, dead)
        order_ids (widgets.TextArea): order id input to exit orders manually
        order_prices (widgets.TextArea): order prices input to exit orders manually
    """

    def __init__(self, obj: Logit):
        """Initialize logit notebook object.

        Args:
            obj (Logit): logit object
        """
        self.obj = obj
        self.layout = widgets.Layout(
            width="auto",
            height="50px",
            display="flex",
            flex_flow="row",
            align_items="center",
        )
        self.layout_hbox = widgets.Layout(
            width="100%",
            display="inline-flex",
            flex_flow="row",
            justify_content="space-around",
        )
        self.layout_hbox_columm = widgets.Layout(
            width="100%",
            display="inline-flex",
            flex_flow="column wrap",
            justify_content="space-around",
            align_items="center",
        )
        self.confirmation_widget = widgets.Button(
            description="Click to confirm your action?",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        self.hbox_confirmation = widgets.HBox(layout=self.layout_hbox)
        self.hbox_confirmation.children = [self.confirmation_widget]
        self.hour_input = widgets.IntText(value=0, description="Hours:", disabled=False)
        self.minute_input = widgets.IntText(
            value=0, description="Minutes:", disabled=False
        )
        self.hour_input_sk = widgets.IntText(
            value=0, description="Hours:", disabled=False
        )
        self.minute_input_sk = widgets.IntText(
            value=0, description="Minutes:", disabled=False
        )
        self.hour_input_cash = widgets.IntText(
            value=0, description="Hours:", disabled=False
        )
        self.minute_input_cash = widgets.IntText(
            value=0, description="Minutes:", disabled=False
        )
        self.strategy_name_error_input = widgets.Text(
            value="",
            description="Enter strategy name",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.segment_name_error_input = widgets.Text(
            value="",
            description="Enter segment",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.ltp_symbols = widgets.Textarea(
            value="[]",
            placeholder="Symbol",
            description="Enter symbol list:",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.ltp_price = widgets.Textarea(
            value="[]",
            placeholder="Prices",
            description="Enter price list:",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.symbol_to_exit = widgets.Text(
            value="",
            description="Symbol to exit",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.spot_price = widgets.FloatText(
            value=0,
            description="Enter spot price",
            dispabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.error_id_input = widgets.IntText(
            value=620001,
            description="Enter error ID",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.strategy_name_input = widgets.Text(
            value="",
            description="Enter strategy name",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.add_error_trade = widgets.RadioButtons(
            options=["Yes", "No"],
            description="Add as error trade",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.wid = widgets.RadioButtons(
            options=["PENDING", "LIVE", "DEAD"], description="Select DF", disabled=False
        )
        self.order_ids = widgets.Textarea(
            value="[]",
            placeholder="Enter order ids",
            description="Order ID list:",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.order_prices = widgets.Textarea(
            value="[]",
            placeholder="Enter prices",
            description="Order prices list:",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.entry_id = widgets.IntText(
            value=620001,
            description="Enter ID",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.entry_segment = widgets.Text(
            value="",
            description="Enter segment",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.entry_symbol = widgets.Text(
            value="",
            description="Enter symbol",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.sharekhan_execution = widgets.Checkbox(
            value=False,
            description="Executed by Sharekhan",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.entry_expiry = widgets.Text(
            value="",
            description="Enter expiry date in DD-Month-YYYY format",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.entry_quantity = widgets.IntText(
            value=0,
            description="Enter quantity",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.entry_strategy = widgets.Text(
            value="",
            description="Enter strategy",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )

        self.entry_wgt_price = widgets.FloatText(
            value=0,
            description="Enter weighted price",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.entry_type = widgets.Text(
            value="",
            description="Enter entry type",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )
        self.entry_strike = widgets.FloatText(
            value=0,
            description="Enter strike",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )

    def ask_confirmation(self, event: widgets, out: widgets):
        """Pops up confirmation widget to the user before performing any action.

        Args:
            event (widgets): trigger widget (Button)
            out (widgets): output widget (display)

        Raises:
            Exception: if confirmation widget is not mapped to any description
        """
        with out:
            out.clear_output()
            description = str(event.description)
            if description in ["Update DataFrame View"]:
                view = str(self.wid.value)
                if view == "LIVE":
                    grid_df = self.obj.df_live
                elif view == "PENDING":
                    grid_df = self.obj.df_pending
                else:
                    grid_df = self.obj.df_dead
                self.confirmation_widget.on_click(
                    functools.partial(update_df_view, df=grid_df, obj=self.obj, out=out)
                )
            elif description in ["Save df Changes"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        save_df_changes,
                        obj=self.obj,
                        view=str(self.wid.value),
                        tmp_df=self.obj.qgrid_widget.get_changed_df(),
                        out=out,
                    )
                )
            elif description in ["Get Last Traded Price"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        get_ltp_notebook,
                        obj=self.obj,
                        hour1=int(self.hour_input.value),
                        minute1=int(self.minute_input.value),
                        symbols=json.loads(self.ltp_symbols.value),
                        prices=json.loads(self.ltp_price.value),
                        add_error_trade=self.add_error_trade.value,
                        out=out,
                    )
                )
            elif description in ["Generate error string using strategy & segment name"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        generate_error_string_seg_strat,
                        obj=self.obj,
                        strategy=str(self.strategy_name_error_input.value),
                        segment=str(self.segment_name_error_input.value),
                        out=out,
                    )
                )
            elif description in ["Exit orders manually"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        exit_orders_manually,
                        obj=self.obj,
                        indexids=json.loads(self.order_ids.value),
                        indexprice=json.loads(self.order_prices.value),
                        sharekhan=self.sharekhan_execution.value,
                        out=out,
                    )
                )
            elif description in ["Enter order manually"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        enter_orders_manually,
                        obj=self.obj,
                        id=self.entry_id.value,
                        segment=self.entry_segment.value,
                        symbol=self.entry_symbol.value,
                        expiry=self.entry_expiry.value,
                        quantity=self.entry_quantity.value,
                        strategy=self.entry_strategy.value,
                        wgt_price=self.entry_wgt_price.value,
                        type=self.entry_type.value,
                        strike=self.entry_strike.value,
                        out=out,
                    )
                )
            elif description in ["Enter order manually to live"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        enter_orders_manually,
                        obj=self.obj,
                        id=self.entry_id.value,
                        segment="FUTCOM",
                        symbol=self.entry_symbol.value,
                        expiry=self.entry_expiry.value,
                        quantity=self.entry_quantity.value,
                        strategy=self.entry_strategy.value,
                        wgt_price=self.entry_wgt_price.value,
                        type="XX",
                        strike=0,
                        out=out,
                    )
                )
            elif description in ["Enter order manually to pending"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        enter_orders_manually_pending,
                        obj=self.obj,
                        id=self.entry_id.value,
                        segment="FUTCOM",
                        symbol=self.entry_symbol.value,
                        expiry=self.entry_expiry.value,
                        quantity=self.entry_quantity.value,
                        strategy=self.entry_strategy.value,
                        wgt_price=self.entry_wgt_price.value,
                        out=out,
                    )
                )
            elif description in ["Add CASH tally mismatch to live orders"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        add_cash_mismatch_to_live_orders,
                        obj=self.obj,
                        hour=self.hour_input_cash.value,
                        minute=self.minute_input_cash.value,
                        out=out,
                    )
                )
            elif description in ["Add Sharekhan position mismatch to live orders"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        add_sharekhan_mismatch_to_live_orders,
                        obj=self.obj,
                        hour=self.hour_input_sk.value,
                        minute=self.minute_input_sk.value,
                        out=out,
                    )
                )
            elif description in ["Add MATLAB mismatch to pending orders"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        add_matlab_trade_to_pending_orders, obj=self.obj, out=out
                    )
                )
            elif description in ["Exit options on expiry date"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        exit_options_on_expiry_date,
                        obj=self.obj,
                        to_exit=str(self.symbol_to_exit.value),
                        nifty_spot=float(self.spot_price.value),
                        out=out,
                    )
                )
            elif description in ["Apply corporate action"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        apply_corporate_action,
                        obj=self.obj,
                        out=out,
                    )
                )
            elif description in ["Build DEAD sheet from Intraday CASHNET"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        dead_sheet_cashnet,
                        obj=self.obj,
                        error_id=str(self.error_id_input.value),
                        strategy_name=str(self.strategy_name_input.value),
                        out=out,
                    )
                )
            elif description in ["Update live sheet"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        live_sheet_processing,
                        obj=self.obj,
                        out=out,
                    )
                )
            elif description in ["Update dead sheet"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        dead_sheet_processing,
                        obj=self.obj,
                        out=out,
                    )
                )
            elif description in ["Add buffered entries to pending"]:
                self.confirmation_widget.on_click(
                    functools.partial(
                        add_order_entries_for_buffered_exits_in_pending,
                        obj=self.obj,
                        out=out,
                    )
                )
            else:
                raise Exception("Confirmation function mapping has been jumbled up")
            display(self.hbox_confirmation)

    def create_status_panel(self):
        """Creates the status panel.

        1) Status : current status of the engine

        2) Live Count: count of live trades

        3) Pending Count: count of pending trades

        4) Dead Count: count of dead trades

        5) Update df view: Watch the current pending, dead & live trade df

        6) Save df changes: Make changes to these df and save them to logit object

        7) Create new error orders

        8) Generate error string
        """
        hbox_heading_status = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_status.children = [widgets.HTML(value="<h1>Status Panel</h1>")]
        display(hbox_heading_status)

        hbox_status = widgets.HBox(layout=self.layout_hbox_columm)
        hbox_status.children = [
            widgets.HTML(value=cstr("Status", color="black", size="h2")),
            self.obj.status,
        ]
        hbox_sz = widgets.HBox(layout=self.layout_hbox)
        hbox_sz.children = [hbox_status]
        display(hbox_sz)

        hbox_pending = widgets.HBox(layout=self.layout_hbox_columm)
        hbox_pending.children = [
            widgets.HTML(value=cstr("Pending Trades", color="green")),
            self.obj.pending_count,
        ]
        hbox_live = widgets.HBox(layout=self.layout_hbox_columm)
        hbox_live.children = [
            widgets.HTML(value=cstr("Live Trades", color="blue")),
            self.obj.live_count,
        ]

        hbox_dead = widgets.HBox(layout=self.layout_hbox_columm)
        hbox_dead.children = [
            widgets.HTML(value=cstr("Dead Trades", color="red")),
            self.obj.dead_count,
        ]

        hbox_last_processed = widgets.HBox(layout=self.layout_hbox_columm)
        hbox_last_processed.children = [
            widgets.HTML(value=cstr("Last Processed Symbol", color="black")),
            self.obj.last_processed,
        ]
        hbox_counts = widgets.HBox(layout=self.layout_hbox)
        hbox_counts.children = [hbox_pending, hbox_live, hbox_dead, hbox_last_processed]
        display(hbox_counts)

        self.layout_hbox = widgets.Layout(
            width="100%",
            display="inline-flex",
            flex_flow="row wrap",
            justify_content="space-around",
        )
        button_update_dataframe_view = widgets.Button(
            description="Update DataFrame View",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_save_df_changes = widgets.Button(
            description="Save df Changes",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
            value="save_df_changes",
        )
        out_status = widgets.Output()
        print("\n")
        button_update_dataframe_view.on_click(
            functools.partial(self.ask_confirmation, out=out_status)
        )
        button_save_df_changes.on_click(
            functools.partial(self.ask_confirmation, out=out_status)
        )
        buttons_hbox = widgets.HBox(layout=self.layout_hbox)
        buttons_hbox.children = [button_update_dataframe_view, button_save_df_changes]
        display(buttons_hbox)
        print("\n")

        hbox_df = widgets.HBox(layout=self.layout_hbox)
        hbox_df.children = [self.wid]
        hbox_df.add_class("box_style")
        display(hbox_df)

        button_create_error = widgets.Button(
            description="Create new error trades",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_generate_error_string = widgets.Button(
            description="Generate Error string",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_create_error.on_click(
            functools.partial(
                error_order,
                obj=self.obj,
                out=out_status,
                flag_mcx=(self.obj.mode != "NSE" and self.obj.mode != "KRX"),
            )
        )
        button_generate_error_string.on_click(
            functools.partial(generate_error_string_df, obj=self.obj, out=out_status)
        )
        hbox_error = widgets.HBox(layout=self.layout_hbox)
        hbox_error.children = [button_create_error, button_generate_error_string]
        display(hbox_error)
        print("\n")

        hbox_output = widgets.HBox(layout=self.layout_hbox)
        hbox_output.children = [out_status]
        display(hbox_output)

    def create_ltp_panel(self):
        """Creates the last traded price panel.

        1) Input hour & time

        2) Enter symbol list (to overwrite)

        3) Enter price list (to overwrite)

        4) Option to add as error trade
        """
        out_ltp = widgets.Output()

        hbox_heading_ltp = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_ltp.children = [
            widgets.HTML(value="<h1>Last Trading Price Panel</h1>")
        ]
        display(hbox_heading_ltp)
        print("\n")

        add_error_trade = widgets.RadioButtons(
            options=["Yes", "No"],
            description="Add as error trade",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-between",
            style={"description_width": "initial"},
        )

        button_get_ltp = widgets.Button(
            description="Get Last Traded Price",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_get_ltp.on_click(functools.partial(self.ask_confirmation, out=out_ltp))
        hbox_ltp = widgets.HBox(layout=self.layout_hbox)
        hbox_ltp.children = [
            self.hour_input,
            self.minute_input,
            add_error_trade,
            self.ltp_symbols,
            self.ltp_price,
            button_get_ltp,
        ]
        hbox_ltp.add_class("box_style")
        display(hbox_ltp)
        print("\n")

        hbox_output_ltp = widgets.HBox(layout=self.layout_hbox)
        hbox_output_ltp.children = [out_ltp]
        display(hbox_output_ltp)

    def create_error_panel(self):
        """Creates the error generation panel.

        1) Generate error using strategy name and segment

        2) Apply coporate action (specifying symbol & adjustment factor)
        """
        hbox_heading_error = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_error.children = [
            widgets.HTML(value="<h1>Error Order Placing & Corporate Action Panel</h1>")
        ]
        display(hbox_heading_error)

        print("\n")
        out_error = widgets.Output()
        button_generate_error_string_segm_strat = widgets.Button(
            description="Generate error string using strategy & segment name",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_generate_error_string_segm_strat.on_click(
            functools.partial(self.ask_confirmation, out=out_error)
        )
        hbox_error_seg_strat = widgets.HBox(layout=self.layout_hbox)
        hbox_error_seg_strat.children = [
            self.strategy_name_error_input,
            self.segment_name_error_input,
            button_generate_error_string_segm_strat,
        ]
        hbox_error_seg_strat.add_class("box_style")
        display(hbox_error_seg_strat)
        print("\n")
        button_apply_corporate_action = widgets.Button(
            description="Apply corporate action",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_apply_corporate_action.on_click(
            functools.partial(self.ask_confirmation, out=out_error)
        )
        hbox_apply_corporate_action = widgets.HBox(layout=self.layout_hbox)
        hbox_apply_corporate_action.children = [
            button_apply_corporate_action,
        ]
        hbox_apply_corporate_action.add_class("box_style")
        display(hbox_apply_corporate_action)

        hbox_out_error = widgets.HBox(layout=self.layout_hbox)
        hbox_out_error.children = [out_error]
        display(hbox_out_error)

    def create_sheet_update_panel(self):
        """Creates the live & dead sheet updation panel.

        1) Update live sheet DB.

        2) Update dead sheet DB.
        """
        hbox_live_dead_sheet = widgets.HBox(layout=self.layout_hbox)
        hbox_live_dead_sheet.children = [
            widgets.HTML(value="<h1>Live & Dead Sheet Panel</h1>")
        ]
        display(hbox_live_dead_sheet)

        print("\n")
        out_live_dead = widgets.Output()
        button_live_sheet_update = widgets.Button(
            description="Update live sheet",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_live_sheet_update.on_click(
            functools.partial(self.ask_confirmation, out=out_live_dead)
        )
        button_dead_sheet_update = widgets.Button(
            description="Update dead sheet",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_dead_sheet_update.on_click(
            functools.partial(self.ask_confirmation, out=out_live_dead)
        )
        hbox_live_dead_sheet_buttons = widgets.HBox(layout=self.layout_hbox)
        hbox_live_dead_sheet_buttons.children = [
            button_live_sheet_update,
            button_dead_sheet_update,
        ]
        hbox_live_dead_sheet_buttons.add_class("box_style")
        display(hbox_live_dead_sheet_buttons)
        print("\n")

        hbox_out_live_dead = widgets.HBox(layout=self.layout_hbox)
        hbox_out_live_dead.children = [out_live_dead]
        display(hbox_out_live_dead)

    def create_exit_entry_panel(self):
        """Creates the exit and entrypanel.

        1) Exitting orders manually

        2) Exitting options on expiry date

        3) Manual entries to live df
        """
        hbox_heading_exit = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_exit.children = [
            widgets.HTML(value="<h1>Order Exit & Entry Panel</h1>")
        ]
        display(hbox_heading_exit)
        print("\n")

        out_exit = widgets.Output()
        button_exit_order_manually = widgets.Button(
            description="Exit orders manually",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_exit_order_manually.on_click(
            functools.partial(self.ask_confirmation, out=out_exit)
        )
        hbox_exit = widgets.HBox(layout=self.layout_hbox)
        hbox_exit.children = [
            self.order_ids,
            self.order_prices,
            self.sharekhan_execution,
            button_exit_order_manually,
        ]
        display(hbox_exit)
        print("\n")
        if self.obj.mode not in ["NSE", "KRX", "US"]:
            button_manual_entries_live = widgets.Button(
                description="Enter order manually to live",
                disabled=False,
                display="flex",
                flex_flow="row",
                align_items="space-around",
                layout=self.layout,
            )
            button_manual_entries_live.on_click(
                functools.partial(self.ask_confirmation, out=out_exit)
            )
            button_manual_entries_pending = widgets.Button(
                description="Enter order manually to pending",
                disabled=False,
                display="flex",
                flex_flow="row",
                align_items="space-around",
                layout=self.layout,
            )
            button_manual_entries_pending.on_click(
                functools.partial(self.ask_confirmation, out=out_exit)
            )
            hbox_entry_order_input1 = widgets.HBox(layout=self.layout_hbox)

            hbox_entry_order_input1.children = [
                self.entry_id,
                self.entry_symbol,
                self.entry_expiry,
            ]
            display(hbox_entry_order_input1)
            hbox_entry_order_input2 = widgets.HBox(layout=self.layout_hbox)
            hbox_entry_order_input2.children = [
                self.entry_wgt_price,
                self.entry_quantity,
                self.entry_strategy,
            ]
            display(hbox_entry_order_input2)
            hbox_entry_button = widgets.HBox(layout=self.layout_hbox)
            hbox_entry_button.children = [
                button_manual_entries_pending,
                button_manual_entries_live,
            ]
            if self.obj.mode in ["COMM"]:
                button_exit_options_on_expiry_day = widgets.Button(
                    description="Exit options on expiry date",
                    disabled=False,
                    display="flex",
                    flex_flow="row",
                    align_items="space-around",
                    layout=self.layout,
                )
                button_exit_options_on_expiry_day.on_click(
                    functools.partial(self.ask_confirmation, out=out_exit)
                )
                hbox_exit_options = widgets.HBox(layout=self.layout_hbox)
                hbox_exit_options.children = [
                    self.symbol_to_exit,
                    self.spot_price,
                    button_exit_options_on_expiry_day,
                ]
                display(hbox_exit_options)
                print("\n")
            display(hbox_entry_button)
        else:
            button_exit_options_on_expiry_day = widgets.Button(
                description="Exit options on expiry date",
                disabled=False,
                display="flex",
                flex_flow="row",
                align_items="space-around",
                layout=self.layout,
            )
            button_exit_options_on_expiry_day.on_click(
                functools.partial(self.ask_confirmation, out=out_exit)
            )
            hbox_exit_options = widgets.HBox(layout=self.layout_hbox)
            hbox_exit_options.children = [
                self.symbol_to_exit,
                self.spot_price,
                button_exit_options_on_expiry_day,
            ]
            display(hbox_exit_options)
            print("\n")

            button_manual_entries = widgets.Button(
                description="Enter order manually",
                disabled=False,
                display="flex",
                flex_flow="row",
                align_items="space-around",
                layout=self.layout,
            )
            button_manual_entries.on_click(
                functools.partial(self.ask_confirmation, out=out_exit)
            )
            hbox_entry_order_input1 = widgets.HBox(layout=self.layout_hbox)

            hbox_entry_order_input1.children = [
                self.entry_id,
                self.entry_segment,
                self.entry_symbol,
            ]
            display(hbox_entry_order_input1)
            hbox_entry_order_input2 = widgets.HBox(layout=self.layout_hbox)
            hbox_entry_order_input2.children = [
                self.entry_expiry,
                self.entry_quantity,
                self.entry_strategy,
            ]
            display(hbox_entry_order_input2)
            hbox_entry_order_input3 = widgets.HBox(layout=self.layout_hbox)
            hbox_entry_order_input3.children = [
                self.entry_wgt_price,
                self.entry_type,
                self.entry_strike,
            ]
            display(hbox_entry_order_input3)
            hbox_entry_button = widgets.HBox(layout=self.layout_hbox)
            hbox_entry_button.children = [button_manual_entries]
            display(hbox_entry_button)

        hbox_output_exit = widgets.HBox(layout=self.layout_hbox)
        hbox_output_exit.children = [out_exit]
        display(hbox_output_exit)

    def create_tally_panel(self):
        """Creates the tally panel.

        1) Tally positions with MATLAB

        2) Adding MATLAB mismatch to pending orders

        3) Tally Sharekhan positions

        4) Tally CASH positions

        5) Adding Sharekhan mismatch to live orders

        6) Adding CASH mismatch to live orders
        """
        hbox_heading_tally = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_tally.children = [widgets.HTML(value="<h1>Order Tally Panel</h1>")]
        display(hbox_heading_tally)
        print("\n")

        out_tally = widgets.Output()
        button_tally_positions = widgets.Button(
            description="Tally position with MATLAB",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_add_matlab_tally_mismatch = widgets.Button(
            description="Add MATLAB mismatch to pending orders",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_position_tally_sharekhan = widgets.Button(
            description="Tally Execution System positions",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_add_shrekhan_tally_mismatch = widgets.Button(
            description="Add Execution System position mismatch to live orders",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_tally_cash_positions = widgets.Button(
            description="Tally CASH positions",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_tally_cash_positions.on_click(
            functools.partial(match_cash_positions, obj=self.obj, out=out_tally)
        )
        button_tally_positions.on_click(
            functools.partial(tally_position_with_matlab, obj=self.obj, out=out_tally)
        )
        button_add_matlab_tally_mismatch.on_click(
            functools.partial(self.ask_confirmation, out=out_tally)
        )
        button_position_tally_sharekhan.on_click(
            functools.partial(tally_shareakhan_positions, obj=self.obj, out=out_tally)
        )
        button_add_shrekhan_tally_mismatch.on_click(
            functools.partial(self.ask_confirmation, out=out_tally)
        )
        position_tally_hbox = widgets.HBox(layout=self.layout_hbox)
        position_tally_hbox.children = [
            button_tally_positions,
            button_add_matlab_tally_mismatch,
            button_position_tally_sharekhan,
            button_tally_cash_positions,
        ]
        display(position_tally_hbox)
        print("\n")
        sharekhan_add_hbox = widgets.HBox(layout=self.layout_hbox)
        sharekhan_add_hbox.children = [
            self.hour_input_sk,
            self.minute_input_sk,
            button_add_shrekhan_tally_mismatch,
        ]
        display(sharekhan_add_hbox)
        print("\n")
        button_add_cash_mismatch = widgets.Button(
            description="Add CASH tally mismatch to live orders",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_add_cash_mismatch.on_click(
            functools.partial(self.ask_confirmation, out=out_tally)
        )
        cash_hbox = widgets.HBox(layout=self.layout_hbox)
        cash_hbox.children = [
            self.hour_input_cash,
            self.minute_input_cash,
            button_add_cash_mismatch,
        ]
        cash_hbox.add_class("box_style")
        display(cash_hbox)
        print("\n")
        button_add_buffered_entries = widgets.Button(
            description="Add buffered entries to pending",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_add_buffered_entries.on_click(
            functools.partial(self.ask_confirmation, out=out_tally)
        )
        buffered_hbox = widgets.HBox(layout=self.layout_hbox)
        buffered_hbox.children = [
            button_add_buffered_entries,
        ]
        buffered_hbox.add_class("box_style")
        display(buffered_hbox)
        print("\n")
        hbox_output_tally = widgets.HBox(layout=self.layout_hbox)
        hbox_output_tally.children = [out_tally]
        display(hbox_output_tally)

    def cashnet_panel(self):
        """Creates the cashnet panel.

        1) Reconcile IntraDay CASHNET position

        2) Building DEAD sheet from Intraday CASHNET
        """
        hbox_heading_cashnet = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_cashnet.children = [
            widgets.HTML(value="<h1>Cashnet & Dead Sheet Panel</h1>")
        ]
        display(hbox_heading_cashnet)
        print("\n")
        out_cashnet_pos = widgets.Output()
        button_reconcile_cashnet_postion = widgets.Button(
            description="Reconcile Intraday CASHNET positions",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_reconcile_cashnet_postion.on_click(
            functools.partial(
                reconcile_intraday_cashnet, obj=self.obj, out=out_cashnet_pos
            )
        )
        hbox_cash_tally = widgets.HBox(layout=self.layout_hbox)
        hbox_cash_tally.children = [button_reconcile_cashnet_postion]
        display(hbox_cash_tally)
        print("\n")

        button_dead_sheet_from_intraday_cashnet = widgets.Button(
            description="Build DEAD sheet from Intraday CASHNET",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_dead_sheet_from_intraday_cashnet.on_click(
            functools.partial(self.ask_confirmation, out=out_cashnet_pos)
        )
        hbox_dead_sheet = widgets.HBox(layout=self.layout_hbox)
        hbox_dead_sheet.children = [
            self.error_id_input,
            self.strategy_name_input,
            button_dead_sheet_from_intraday_cashnet,
        ]
        hbox_dead_sheet.add_class("box_style")
        display(hbox_dead_sheet)

        hbox_output_cashnet = widgets.HBox(layout=self.layout_hbox)
        hbox_output_cashnet.children = [out_cashnet_pos]
        display(hbox_output_cashnet)

    def create_dump_panel(self):
        """Createst the dump panel.

        1) Dump logit object to csv

        2) Uploading created files to file server

        3) Loading object from csv
        """
        hbox_heading_dump = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_dump.children = [
            widgets.HTML(value="<h1>Saving & Uploading Panel</h1>")
        ]
        display(hbox_heading_dump)
        print("\n")

        out_dump = widgets.Output()
        button_dump_to_csv = widgets.Button(
            description="Dump object to csv",
            disabled=False,
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )

        button_load_from_csv = widgets.Button(
            description="Load object from csv",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_load_from_csv.on_click(
            functools.partial(load_csv, obj=self.obj, out=out_dump, location="csv/")
        )
        button_dump_to_csv.on_click(
            functools.partial(dump_csv, obj=self.obj, out=out_dump, location="csv/")
        )
        hbox_dump_upload = widgets.HBox(layout=self.layout_hbox)
        hbox_dump_upload.children = [
            button_dump_to_csv,
            button_load_from_csv,
        ]
        hbox_dump_upload.add_class("box_style")
        display(hbox_dump_upload)
        print("\n")
        button_load_to_csv_live = widgets.Button(
            description="Load live sheet from csv",
            disabled=False,
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_load_to_csv_dead = widgets.Button(
            description="Load dead sheet from csv",
            disabled=False,
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_load_to_csv_pending = widgets.Button(
            description="Load pending orders from csv",
            disabled=False,
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_load_to_csv_buffer = widgets.Button(
            description="Load buffered orders from csv",
            disabled=False,
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_load_to_csv_live.on_click(
            functools.partial(
                load_csv_df,
                obj=self.obj,
                out=out_dump,
                identifier="live",
                location="csv/",
            )
        )
        button_load_to_csv_dead.on_click(
            functools.partial(
                load_csv_df,
                obj=self.obj,
                out=out_dump,
                identifier="dead",
                location="csv/",
            )
        )
        button_load_to_csv_pending.on_click(
            functools.partial(
                load_csv_df,
                obj=self.obj,
                out=out_dump,
                identifier="pending",
                location="csv/",
            )
        )
        button_load_to_csv_buffer.on_click(
            functools.partial(
                load_csv_df,
                obj=self.obj,
                out=out_dump,
                identifier="buffered",
                location="csv/",
            )
        )
        hbox_load_df = widgets.HBox(layout=self.layout_hbox)
        hbox_load_df.children = [
            button_load_to_csv_live,
            button_load_to_csv_dead,
            button_load_to_csv_pending,
            button_load_to_csv_buffer,
        ]
        hbox_load_df.add_class("box_style")
        display(hbox_load_df)
        print("\n")
        hbox_output_dump = widgets.HBox(layout=self.layout_hbox)
        hbox_output_dump.children = [out_dump]
        display(hbox_output_dump)

    def create_eod_panel(self):
        """Creates the eod function panel.

        For NSE:
            1) Running EOD checks

        For commodities:
            1) Running EOD checks

        For KRX:
            1) Running EOD checks
        """
        hbox_heading_morning_evening = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_morning_evening.children = [
            widgets.HTML(value="<h1>EOD Checks</h1>")
        ]
        display(hbox_heading_morning_evening)
        print("\n")

        out_morning_evening = widgets.Output()
        button_eod_checks = widgets.Button(
            description="Run End of Day Checks",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_eod_checks.on_click(
            functools.partial(eod_checks, obj=self.obj, out=out_morning_evening)
        )
        hbox_eod = widgets.HBox(layout=self.layout_hbox)
        hbox_eod.children = [
            button_eod_checks,
        ]
        hbox_eod.add_class("box_style")
        display(hbox_eod)
        hbox_output_morning_evening = widgets.HBox(layout=self.layout_hbox)
        hbox_output_morning_evening.children = [out_morning_evening]
        display(hbox_output_morning_evening)

    def create_poss_tally_comm(self):
        """Creates the tally panel for commodities.

        1) Tally positions with MATLAB

        2) Tally position with execution system
        """
        hbox_heading_tally = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_tally.children = [widgets.HTML(value="<h1>Order Tally Panel</h1>")]
        display(hbox_heading_tally)
        print("\n")
        out_tally = widgets.Output()
        button_tally_positions = widgets.Button(
            description="Tally position with MATLAB",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_position_tally_sharekhan = widgets.Button(
            description="Tally Execution System positions",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_tally_positions.on_click(
            functools.partial(
                tally_position_with_matlab,
                obj=self.obj,
                out=out_tally,
            )
        )
        button_position_tally_sharekhan.on_click(
            functools.partial(
                tally_shareakhan_positions,
                obj=self.obj,
                out=out_tally,
                flag_mcx=(self.obj.mode != "NSE" and self.obj.mode != "KRX"),
            )
        )
        position_tally_hbox = widgets.HBox(layout=self.layout_hbox)
        position_tally_hbox.children = [
            button_tally_positions,
            button_position_tally_sharekhan,
        ]
        display(position_tally_hbox)
        print("\n")
        hbox_output_tally = widgets.HBox(layout=self.layout_hbox)
        hbox_output_tally.children = [out_tally]
        display(hbox_output_tally)

    def create_poss_tally_krx(self):
        """Creates the tally panel for krx.

        Tally positions with MATLAB (BaLTE_OMS)

        """
        hbox_heading_tally = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_tally.children = [widgets.HTML(value="<h1>Order Tally Panel</h1>")]
        display(hbox_heading_tally)
        print("\n")
        out_tally = widgets.Output()
        button_tally_positions = widgets.Button(
            description="Tally position with MATLAB",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_tally_positions.on_click(
            functools.partial(
                tally_position_with_matlab,
                obj=self.obj,
                out=out_tally,
            )
        )
        position_tally_hbox = widgets.HBox(layout=self.layout_hbox)
        position_tally_hbox.children = [
            button_tally_positions,
        ]
        display(position_tally_hbox)
        print("\n")
        hbox_output_tally = widgets.HBox(layout=self.layout_hbox)
        hbox_output_tally.children = [out_tally]
        display(hbox_output_tally)

    def create_ncdex_execution_panel(self):
        """Create the NCDEX Order execution panel."""
        hbox_heading_ncdex = widgets.HBox(layout=self.layout_hbox)
        hbox_heading_ncdex.children = [
            widgets.HTML(value="<h1>NCDEX Order Execution Panel</h1>")
        ]
        display(hbox_heading_ncdex)
        print("\n")
        out_ncdex = widgets.Output()
        button_ncdex_execution = widgets.Button(
            description="Execute NCDEX Orders",
            disabled=False,
            display="flex",
            flex_flow="row",
            align_items="space-around",
            layout=self.layout,
        )
        button_ncdex_execution.on_click(
            functools.partial(
                ncdex_pricing,
                obj=self.obj,
                out=out_ncdex,
            )
        )
        position_ncdex_hbox = widgets.HBox(layout=self.layout_hbox)
        position_ncdex_hbox.children = [
            button_ncdex_execution,
        ]
        display(position_ncdex_hbox)
        print("\n")
        hbox_output_ncdex = widgets.HBox(layout=self.layout_hbox)
        hbox_output_ncdex.children = [out_ncdex]
        display(hbox_output_ncdex)
