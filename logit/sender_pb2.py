# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: sender.proto

import sys

_b = sys.version_info[0] < 3 and (lambda x: x) or (lambda x: x.encode("latin1"))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor.FileDescriptor(
    name="sender.proto",
    package="chunkerservice",
    syntax="proto3",
    serialized_options=None,
    serialized_pb=_b(
        '\n\x0csender.proto\x12\x0e\x63hunkerservice"B\n\x14ListOfResponseArrays\x12*\n\x03\x61rr\x18\x01 \x03(\x0b\x32\x1d.chunkerservice.ResponseArray"Q\n\x0e\x43hunkerRequest\x12\x0f\n\x07segment\x18\x01 \x01(\t\x12\x0c\n\x04\x66req\x18\x02 \x01(\t\x12\r\n\x05\x64type\x18\x03 \x01(\t\x12\x11\n\ttimestamp\x18\x04 \x01(\t"@\n\x10TimeStampRequest\x12\x0f\n\x07segment\x18\x01 \x01(\t\x12\x0c\n\x04\x66req\x18\x02 \x01(\t\x12\r\n\x05\x64type\x18\x03 \x01(\t" \n\rResponseArray\x12\x0f\n\x07message\x18\x01 \x03(\t"%\n\x12RepeatedArrayReply\x12\x0f\n\x07message\x18\x01 \x03(\t"&\n\x11TimestampResponse\x12\x11\n\ttimestamp\x18\x01 \x01(\t"\xa1\x01\n\x10\x41llResponseArray\x12>\n\x07message\x18\x01 \x03(\x0b\x32-.chunkerservice.AllResponseArray.MessageEntry\x1aM\n\x0cMessageEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12,\n\x05value\x18\x02 \x01(\x0b\x32\x1d.chunkerservice.ResponseArray:\x02\x38\x01\x32\x8c\x02\n\x07\x43hunker\x12K\n\x08get_data\x12\x1e.chunkerservice.ChunkerRequest\x1a\x1d.chunkerservice.ResponseArray"\x00\x12]\n\x14get_latest_timestamp\x12 .chunkerservice.TimeStampRequest\x1a!.chunkerservice.TimestampResponse"\x00\x12U\n\x0cget_all_data\x12!.chunkerservice.TimestampResponse\x1a .chunkerservice.AllResponseArray"\x00\x62\x06proto3'
    ),
)


_LISTOFRESPONSEARRAYS = _descriptor.Descriptor(
    name="ListOfResponseArrays",
    full_name="chunkerservice.ListOfResponseArrays",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="arr",
            full_name="chunkerservice.ListOfResponseArrays.arr",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=32,
    serialized_end=98,
)


_CHUNKERREQUEST = _descriptor.Descriptor(
    name="ChunkerRequest",
    full_name="chunkerservice.ChunkerRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="segment",
            full_name="chunkerservice.ChunkerRequest.segment",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="freq",
            full_name="chunkerservice.ChunkerRequest.freq",
            index=1,
            number=2,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="dtype",
            full_name="chunkerservice.ChunkerRequest.dtype",
            index=2,
            number=3,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="timestamp",
            full_name="chunkerservice.ChunkerRequest.timestamp",
            index=3,
            number=4,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=100,
    serialized_end=181,
)


_TIMESTAMPREQUEST = _descriptor.Descriptor(
    name="TimeStampRequest",
    full_name="chunkerservice.TimeStampRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="segment",
            full_name="chunkerservice.TimeStampRequest.segment",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="freq",
            full_name="chunkerservice.TimeStampRequest.freq",
            index=1,
            number=2,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="dtype",
            full_name="chunkerservice.TimeStampRequest.dtype",
            index=2,
            number=3,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=183,
    serialized_end=247,
)


_RESPONSEARRAY = _descriptor.Descriptor(
    name="ResponseArray",
    full_name="chunkerservice.ResponseArray",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="message",
            full_name="chunkerservice.ResponseArray.message",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=249,
    serialized_end=281,
)


_REPEATEDARRAYREPLY = _descriptor.Descriptor(
    name="RepeatedArrayReply",
    full_name="chunkerservice.RepeatedArrayReply",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="message",
            full_name="chunkerservice.RepeatedArrayReply.message",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=283,
    serialized_end=320,
)


_TIMESTAMPRESPONSE = _descriptor.Descriptor(
    name="TimestampResponse",
    full_name="chunkerservice.TimestampResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="timestamp",
            full_name="chunkerservice.TimestampResponse.timestamp",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=322,
    serialized_end=360,
)


_ALLRESPONSEARRAY_MESSAGEENTRY = _descriptor.Descriptor(
    name="MessageEntry",
    full_name="chunkerservice.AllResponseArray.MessageEntry",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="key",
            full_name="chunkerservice.AllResponseArray.MessageEntry.key",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=_b("").decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
        _descriptor.FieldDescriptor(
            name="value",
            full_name="chunkerservice.AllResponseArray.MessageEntry.value",
            index=1,
            number=2,
            type=11,
            cpp_type=10,
            label=1,
            has_default_value=False,
            default_value=None,
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=_b("8\001"),
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=447,
    serialized_end=524,
)

_ALLRESPONSEARRAY = _descriptor.Descriptor(
    name="AllResponseArray",
    full_name="chunkerservice.AllResponseArray",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    fields=[
        _descriptor.FieldDescriptor(
            name="message",
            full_name="chunkerservice.AllResponseArray.message",
            index=0,
            number=1,
            type=11,
            cpp_type=10,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
        ),
    ],
    extensions=[],
    nested_types=[_ALLRESPONSEARRAY_MESSAGEENTRY],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=363,
    serialized_end=524,
)

_LISTOFRESPONSEARRAYS.fields_by_name["arr"].message_type = _RESPONSEARRAY
_ALLRESPONSEARRAY_MESSAGEENTRY.fields_by_name["value"].message_type = _RESPONSEARRAY
_ALLRESPONSEARRAY_MESSAGEENTRY.containing_type = _ALLRESPONSEARRAY
_ALLRESPONSEARRAY.fields_by_name[
    "message"
].message_type = _ALLRESPONSEARRAY_MESSAGEENTRY
DESCRIPTOR.message_types_by_name["ListOfResponseArrays"] = _LISTOFRESPONSEARRAYS
DESCRIPTOR.message_types_by_name["ChunkerRequest"] = _CHUNKERREQUEST
DESCRIPTOR.message_types_by_name["TimeStampRequest"] = _TIMESTAMPREQUEST
DESCRIPTOR.message_types_by_name["ResponseArray"] = _RESPONSEARRAY
DESCRIPTOR.message_types_by_name["RepeatedArrayReply"] = _REPEATEDARRAYREPLY
DESCRIPTOR.message_types_by_name["TimestampResponse"] = _TIMESTAMPRESPONSE
DESCRIPTOR.message_types_by_name["AllResponseArray"] = _ALLRESPONSEARRAY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListOfResponseArrays = _reflection.GeneratedProtocolMessageType(
    "ListOfResponseArrays",
    (_message.Message,),
    {
        "DESCRIPTOR": _LISTOFRESPONSEARRAYS,
        "__module__": "sender_pb2",
        # @@protoc_insertion_point(class_scope:chunkerservice.ListOfResponseArrays)
    },
)
_sym_db.RegisterMessage(ListOfResponseArrays)

ChunkerRequest = _reflection.GeneratedProtocolMessageType(
    "ChunkerRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _CHUNKERREQUEST,
        "__module__": "sender_pb2",
        # @@protoc_insertion_point(class_scope:chunkerservice.ChunkerRequest)
    },
)
_sym_db.RegisterMessage(ChunkerRequest)

TimeStampRequest = _reflection.GeneratedProtocolMessageType(
    "TimeStampRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _TIMESTAMPREQUEST,
        "__module__": "sender_pb2",
        # @@protoc_insertion_point(class_scope:chunkerservice.TimeStampRequest)
    },
)
_sym_db.RegisterMessage(TimeStampRequest)

ResponseArray = _reflection.GeneratedProtocolMessageType(
    "ResponseArray",
    (_message.Message,),
    {
        "DESCRIPTOR": _RESPONSEARRAY,
        "__module__": "sender_pb2",
        # @@protoc_insertion_point(class_scope:chunkerservice.ResponseArray)
    },
)
_sym_db.RegisterMessage(ResponseArray)

RepeatedArrayReply = _reflection.GeneratedProtocolMessageType(
    "RepeatedArrayReply",
    (_message.Message,),
    {
        "DESCRIPTOR": _REPEATEDARRAYREPLY,
        "__module__": "sender_pb2",
        # @@protoc_insertion_point(class_scope:chunkerservice.RepeatedArrayReply)
    },
)
_sym_db.RegisterMessage(RepeatedArrayReply)

TimestampResponse = _reflection.GeneratedProtocolMessageType(
    "TimestampResponse",
    (_message.Message,),
    {
        "DESCRIPTOR": _TIMESTAMPRESPONSE,
        "__module__": "sender_pb2",
        # @@protoc_insertion_point(class_scope:chunkerservice.TimestampResponse)
    },
)
_sym_db.RegisterMessage(TimestampResponse)

AllResponseArray = _reflection.GeneratedProtocolMessageType(
    "AllResponseArray",
    (_message.Message,),
    {
        "MessageEntry": _reflection.GeneratedProtocolMessageType(
            "MessageEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _ALLRESPONSEARRAY_MESSAGEENTRY,
                "__module__": "sender_pb2",
                # @@protoc_insertion_point(class_scope:chunkerservice.AllResponseArray.MessageEntry)
            },
        ),
        "DESCRIPTOR": _ALLRESPONSEARRAY,
        "__module__": "sender_pb2",
        # @@protoc_insertion_point(class_scope:chunkerservice.AllResponseArray)
    },
)
_sym_db.RegisterMessage(AllResponseArray)
_sym_db.RegisterMessage(AllResponseArray.MessageEntry)


_ALLRESPONSEARRAY_MESSAGEENTRY._options = None

_CHUNKER = _descriptor.ServiceDescriptor(
    name="Chunker",
    full_name="chunkerservice.Chunker",
    file=DESCRIPTOR,
    index=0,
    serialized_options=None,
    serialized_start=527,
    serialized_end=795,
    methods=[
        _descriptor.MethodDescriptor(
            name="get_data",
            full_name="chunkerservice.Chunker.get_data",
            index=0,
            containing_service=None,
            input_type=_CHUNKERREQUEST,
            output_type=_RESPONSEARRAY,
            serialized_options=None,
        ),
        _descriptor.MethodDescriptor(
            name="get_latest_timestamp",
            full_name="chunkerservice.Chunker.get_latest_timestamp",
            index=1,
            containing_service=None,
            input_type=_TIMESTAMPREQUEST,
            output_type=_TIMESTAMPRESPONSE,
            serialized_options=None,
        ),
        _descriptor.MethodDescriptor(
            name="get_all_data",
            full_name="chunkerservice.Chunker.get_all_data",
            index=2,
            containing_service=None,
            input_type=_TIMESTAMPRESPONSE,
            output_type=_ALLRESPONSEARRAY,
            serialized_options=None,
        ),
    ],
)
_sym_db.RegisterServiceDescriptor(_CHUNKER)

DESCRIPTOR.services_by_name["Chunker"] = _CHUNKER

# @@protoc_insertion_point(module_scope)
