site_name: Logit


theme: 
  name: "material"
  features:
  - search.suggest
  - search.highlight
  - content.tabs.link
  - header.autohide

markdown_extensions:
- toc:
    permalink: true
- markdown.extensions.codehilite:
    guess_lang: false
- admonition
- codehilite
- extra
- pymdownx.superfences:
    custom_fences:
    - name: mermaid
      class: mermaid
      format: !!python/name:pymdownx.superfences.fence_code_format ''
- pymdownx.tabbed:
    alternate_style: true

plugins:
  - search
  - autorefs
  - mkdocstrings
  - table-reader

nav:
  - index.md
  - Architecture:
    - Architecture/status_panel.md
    - Architecture/last_trading_price_panel.md
    - Architecture/error_order_corpact_panel.md
    - Architecture/ncdex_execution_panel.md
    - Architecture/live_dead_sheet_panel.md
    - Architecture/order_exit_entry_panel.md
    - Architecture/order_tally_panel.md
    - Architecture/cashnet_dead_sheet_panel.md
    - Architecture/saving_uploading_panel.md
    - Architecture/eod_panel.md
    - Architecture/notebook_functions.md
  - Tutorials:
    - Tutorials/tutorial.md
    - Tutorials/morning_processes.md
    - Tutorials/dataframe_tutorial.md
    - Tutorials/error_generation_tutorial.md
    - Tutorials/corpact_tutorial.md
    - Tutorials/dumping_tutorial.md
    - Tutorials/entry_tutorial.md
    - Tutorials/eod_tutorial.md
    - Tutorials/exit_tutorial.md
    - Tutorials/live_dead_sheet_tutorial.md
    - Tutorials/ltp_tutorial.md
    - Tutorials/tally_cash_tutorial.md
    - Tutorials/tally_execution_system_tutorial.md
    - Tutorials/tally_matlab_tutorial.md
  - Utility:
    - Utility/parser.md
    - Utility/data_utils.md
    - Utility/df_compare.md
    - Utility/mtm_utils.md
    - Utility/trade_utils.md
    - Utility/miscellaneous.md
  - References:
    - References/logit.md
    - References/logit_jupyter.md