# Morning Process
⚠️**WARNING**⚠️ The user must be careful as this option will update the database ledger.

> **Note:** Select *YES* while initialising the logit object before running this to load the previous day's dead sheet.

## NSE

* Run the **Calculate the currency database variables** cell in the notebook.

`This updates the currency ledger. After inputting the required variables, it will process the information and then raise a prompt asking the user if the variables look fine and should be updated in the db.`

* Run the **Calculate the morning variables** cell in t

`This updates the eqledger. You will prompted before these variables are entered into the db.`

> **Note:** After each process, you will be prompted to ask if you want to clear the dead table. This should only be done after both the processes are complete.

## COMMODITIES

* Run the **Morning Process** cell in the notebook.

`This updates the commodities ledger. After inputting the required variables, it will process the information and then raise a prompt asking the user if the variables look fine and should be updated in the db.`


> **Note:** After this process, you will be prompted to ask if you want to clear the dead table. This should be done after the process is successfully completed.