# Starting the application

Run all the cells in the jupyter notebook. It will import all the necessary libraries and initiliase the **Kafka consumer** along with the **logit** and **logit jupyter notebook** object.

> While running the cell which initialises all the objects, live sheet for previous working day will be loaded and you will be prompted to select if you want to update the ledger.

> If you say **YES** then the dead sheet will also be automatically loaded from minio as it required to update the ledger.