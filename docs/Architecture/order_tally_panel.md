# Order Tally Panel

## Tally position with MATLAB

::: logit.logit_notebook_functions.tally_position_with_matlab

## Add MATLAB mismatch to pending orders

::: logit.logit_notebook_functions.add_matlab_trade_to_pending_orders

## Tally Execution System positions

::: logit.logit_notebook_functions.tally_shareakhan_positions

## Add Execution System position mismatch to live orders

::: logit.logit_notebook_functions.add_sharekhan_mismatch_to_live_orders

## Tally Cash positions

::: logit.logit_notebook_functions.match_cash_positions

## Add Cash position mismatch to live orders

::: logit.logit_notebook_functions.add_cash_mismatch_to_live_orders