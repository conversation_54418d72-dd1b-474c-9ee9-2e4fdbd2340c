# Status Panel

It has 5 counters displaying important information when active.

1. **Status**: Displays if the engine is Idle, Running or Stopped
2. **Pending Trades**: Displays the number of pending trades
3. **Live Trades**: Displays the number of live trades (entered but not exited yet)
4. **Dead Trades**: Displays the number of fully executed trades
5. **Last Processed Symbol**: Displays the last processed symbol

## Updating dataframe view
It is used in conjunction with radio buttons which provide the user with a very simple and interactive way to select their view (live, pending or dead trades).

::: logit.logit_notebook_functions.update_df_view

## Save dataframe changes

::: logit.logit_notebook_functions.save_df_changes

## Create new error trades

::: logit.logit_notebook_functions.error_order

## Create error string

::: logit.logit_notebook_functions.generate_error_string_df