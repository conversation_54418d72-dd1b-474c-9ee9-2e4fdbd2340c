# Dataframe comparing & mismatch

## Compare dataframe

::: logit.utility.compare_dataframe

## Compare df

::: logit.utility.compare_df

## Comparing df with quantities

::: logit.utility.compare_df_with_quantities

## Create position tally mismatch as error dataframe

::: logit.utility.create_pos_tally_error_df

## Create MATLAB mismatch as pending daataframe

::: logit.utility.create_matlab_tally_pending_df